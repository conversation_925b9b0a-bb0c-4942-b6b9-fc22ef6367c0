/* 传感器校准页面样式 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 头部 */
.header {
  text-align: center;
  padding: 40rpx 0;
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 校准状态卡片 */
.status-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.status-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status-indicator {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-indicator.pending {
  background: #fff3cd;
  color: #856404;
}

.status-indicator.success {
  background: #d4edda;
  color: #155724;
}

.status-indicator.partial {
  background: #cce7ff;
  color: #004085;
}

.status-indicator.error {
  background: #f8d7da;
  color: #721c24;
}

/* 校准进度 */
.calibration-progress {
  margin-top: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #45a049 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 26rpx;
  color: #666;
  text-align: center;
}

/* 校准项目 */
.calibration-items {
  margin-bottom: 30rpx;
}

.calibration-item {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.acc-icon {
  background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
}

.gyro-icon {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.compass-icon {
  background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.item-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}

.item-status {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.item-status.pending {
  background: #fff3cd;
}

.item-status.success {
  background: #d4edda;
}

.item-status.error {
  background: #f8d7da;
}

.status-icon {
  font-size: 32rpx;
}

/* 详情展示 */
.item-details {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 20rpx 0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

/* 操作按钮 */
.item-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.action-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 36rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn[disabled] {
  opacity: 0.5;
  transform: none !important;
}

/* 校准指导 */
.guidance-card {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.guidance-header {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 30rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.guidance-title {
  font-size: 32rpx;
  font-weight: bold;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.guidance-content {
  padding: 40rpx;
}

.guidance-step {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.guidance-step:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

/* 一键校准 */
.quick-calibration {
  margin-bottom: 30rpx;
}

.quick-btn {
  width: 100%;
  height: 120rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #ee5a52 100%);
  color: white;
  border-radius: 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.quick-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.quick-btn[disabled] {
  opacity: 0.6;
  transform: none !important;
}

.btn-icon {
  font-size: 36rpx;
}

/* 底部操作 */
.bottom-actions {
  display: flex;
  gap: 20rpx;
  padding-bottom: 40rpx;
}

.action-button {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-button.primary {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
}

.action-button.secondary {
  background: white;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.action-button:active {
  transform: translateY(2rpx);
}

.action-button[disabled] {
  opacity: 0.5;
  transform: none !important;
}

/* 校准动画 */
.calibration-animation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.animation-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  max-width: 600rpx;
  margin: 40rpx;
}

.device-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  display: block;
}

.device-icon.static {
  animation: none;
}

.device-icon.figure8 {
  animation: figure8 3s ease-in-out infinite;
}

@keyframes figure8 {
  0% { transform: translateX(0) rotate(0deg); }
  25% { transform: translateX(20rpx) rotate(90deg); }
  50% { transform: translateX(0) rotate(180deg); }
  75% { transform: translateX(-20rpx) rotate(270deg); }
  100% { transform: translateX(0) rotate(360deg); }
}

.animation-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 40rpx;
  display: block;
  line-height: 1.5;
}

.animation-indicator {
  display: flex;
  justify-content: center;
  gap: 16rpx;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #667eea;
  animation: pulse 1.5s ease-in-out infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.3s;
}

.dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* 提示信息 */
.toast {
  position: fixed;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1001;
}

.toast.show {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 15rpx;
  }
  
  .calibration-item {
    padding: 30rpx;
  }
  
  .item-icon {
    width: 60rpx;
    height: 60rpx;
    font-size: 32rpx;
    margin-right: 20rpx;
  }
  
  .item-title {
    font-size: 28rpx;
  }
  
  .item-desc {
    font-size: 24rpx;
  }
}