/**
 * 错误处理测试脚本
 * 用于验证文件系统错误修复效果
 */

// 模拟微信小程序环境
const mockWx = {
  onError: (callback) => {
    console.log('已注册微信错误监听器');
    // 模拟一个文件系统错误
    setTimeout(() => {
      callback(new Error("no such file or directory, access 'wxfile://usr/miniprogramLog/log1'"));
    }, 1000);
  },
  
  onUnhandledRejection: (callback) => {
    console.log('已注册Promise拒绝监听器');
  },
  
  getFileSystemManager: () => {
    return {
      access: (options) => {
        // 模拟文件系统访问失败
        setTimeout(() => {
          options.fail && options.fail({
            errMsg: "access:fail no such file or directory, open 'wxfile://usr/miniprogramLog/'"
          });
        }, 100);
      },
      
      readFile: (options) => {
        setTimeout(() => {
          options.fail && options.fail({
            errMsg: "readFile:fail no such file or directory"
          });
        }, 100);
      },
      
      writeFile: (options) => {
        setTimeout(() => {
          options.fail && options.fail({
            errMsg: "writeFile:fail permission denied"
          });
        }, 100);
      }
    };
  },
  
  getSystemInfoSync: () => ({
    platform: 'devtools',
    version: '8.0.5',
    benchmarkLevel: 1
  }),
  
  setStorageSync: (key, data) => {
    console.log(`模拟存储: ${key} =`, data);
  },
  
  getStorageSync: (key) => {
    console.log(`模拟获取存储: ${key}`);
    return null;
  },
  
  env: {
    USER_DATA_PATH: '/mock/user/data'
  }
};

// 设置全局wx对象
global.wx = mockWx;
global.console = {
  ...console,
  originalError: console.error
};

// 导入要测试的模块
const ErrorHandler = require('../utils/ErrorHandler.js');
const SafeFileSystem = require('../utils/SafeFileSystem.js');

/**
 * 测试错误处理器
 */
async function testErrorHandler() {
  console.log('\n=== 测试错误处理器 ===');
  
  const errorHandler = new ErrorHandler();
  
  // 测试初始化
  errorHandler.init();
  console.log('✅ 错误处理器初始化完成');
  
  // 测试文件系统错误检测
  const fileSystemErrors = [
    "no such file or directory, access 'wxfile://usr/miniprogramLog/'",
    "access:fail wxfile://usr/miniprogramLog/log1",
    "permission denied: /usr/miniprogramLog/",
    "ENOENT: no such file or directory"
  ];
  
  fileSystemErrors.forEach((error, index) => {
    const isFileSystemError = errorHandler.isFileSystemError(error);
    console.log(`${index + 1}. "${error.substring(0, 50)}..." -> ${isFileSystemError ? '✅' : '❌'} 文件系统错误`);
  });
  
  // 测试错误处理
  console.log('\n--- 测试错误处理 ---');
  errorHandler.handleError('test', "no such file or directory, access 'wxfile://usr/miniprogramLog/'");
  errorHandler.handleError('test', "sensor error: accelerometer not available");
  errorHandler.handleError('test', "network error: request timeout");
  
  // 获取统计信息
  const stats = errorHandler.getErrorStats();
  console.log('\n--- 错误统计 ---');
  console.log('文件系统错误:', stats.fileSystemErrors);
  console.log('传感器错误:', stats.sensorErrors);
  console.log('网络错误:', stats.networkErrors);
  console.log('总错误数:', stats.totalErrors);
  
  // 获取健康状态
  const health = errorHandler.getHealthStatus();
  console.log('\n--- 健康状态 ---');
  console.log('整体状态:', health.overall);
  console.log('文件系统健康:', health.fileSystemHealth);
  console.log('传感器健康:', health.sensorHealth);
  
  return errorHandler;
}

/**
 * 测试安全文件系统
 */
async function testSafeFileSystem() {
  console.log('\n=== 测试安全文件系统 ===');
  
  const safeFS = new SafeFileSystem();
  
  // 测试健康状态
  const health = safeFS.getHealthStatus();
  console.log('文件系统可用性:', health.available ? '✅' : '❌');
  console.log('管理器状态:', health.manager ? '✅' : '❌');
  
  // 测试安全存储
  console.log('\n--- 测试安全存储 ---');
  const setResult = await safeFS.safeSetStorage('test_key', { data: 'test_value' });
  console.log('存储结果:', setResult ? '✅' : '❌');
  
  const getData = await safeFS.safeGetStorage('test_key', 'default');
  console.log('获取数据:', getData);
  
  // 测试文件访问（会失败但不会抛出错误）
  console.log('\n--- 测试文件访问 ---');
  try {
    const accessResult = await safeFS.access('wxfile://usr/miniprogramLog/');
    console.log('文件访问结果:', accessResult ? '✅ 可访问' : '❌ 不可访问');
  } catch (error) {
    console.log('❌ 文件访问异常:', error.message);
  }
  
  // 测试文件读取（会失败但不会抛出错误）
  try {
    const readResult = await safeFS.readFile('wxfile://usr/miniprogramLog/log1');
    console.log('文件读取结果:', readResult ? '✅ 成功' : '❌ 失败（已处理）');
  } catch (error) {
    console.log('❌ 文件读取异常:', error.message);
  }
  
  return safeFS;
}

/**
 * 测试控制台拦截
 */
function testConsoleInterception() {
  console.log('\n=== 测试控制台拦截 ===');
  
  // 这些错误应该被拦截和处理
  console.error("error occurs:no such file or directory, access 'wxfile://usr/miniprogramLog/'");
  console.error("error occurs:no such file or directory, access 'wxfile://usr/miniprogramLog/log1'");
  console.error("access:fail permission denied wxfile://usr/miniprogramLog/");
  
  // 这些错误应该正常显示
  console.error("这是一个正常的错误信息");
  console.error("网络请求失败");
  
  console.log('✅ 控制台拦截测试完成');
}

/**
 * 模拟实际使用场景
 */
async function simulateRealUsage() {
  console.log('\n=== 模拟实际使用场景 ===');
  
  const errorHandler = new ErrorHandler();
  const safeFS = new SafeFileSystem();
  
  errorHandler.init();
  
  // 模拟应用启动时的文件系统错误
  console.log('1. 模拟应用启动...');
  setTimeout(() => {
    // 这个错误会被自动处理
    console.error("error occurs:no such file or directory, access 'wxfile://usr/miniprogramLog/'");
  }, 500);
  
  // 模拟传感器启动
  console.log('2. 模拟传感器启动...');
  setTimeout(() => {
    errorHandler.handleError('sensor', 'accelerometer initialization failed');
    errorHandler.handleError('sensor', 'compass not available');
  }, 1000);
  
  // 模拟数据保存
  console.log('3. 模拟数据保存...');
  setTimeout(async () => {
    const saveResult = await safeFS.safeSetStorage('trajectory_data', {
      points: [{ x: 0, y: 0, timestamp: Date.now() }],
      metadata: { version: '1.0' }
    });
    console.log('数据保存结果:', saveResult ? '✅' : '❌');
  }, 1500);
  
  // 等待所有异步操作完成
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 输出最终统计
  const finalStats = errorHandler.getErrorStats();
  console.log('\n--- 最终统计 ---');
  console.log('处理的文件系统错误:', finalStats.fileSystemErrors);
  console.log('处理的传感器错误:', finalStats.sensorErrors);
  console.log('总处理错误数:', finalStats.totalErrors);
  
  const finalHealth = errorHandler.getHealthStatus();
  console.log('\n--- 最终健康状态 ---');
  console.log('系统整体健康:', finalHealth.overall);
  console.log('所有组件状态:', finalHealth);
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始错误处理测试...\n');
  
  try {
    // 测试错误处理器
    const errorHandler = await testErrorHandler();
    
    // 测试安全文件系统
    const safeFS = await testSafeFileSystem();
    
    // 测试控制台拦截
    testConsoleInterception();
    
    // 模拟实际使用
    await simulateRealUsage();
    
    console.log('\n🎉 所有测试完成！');
    console.log('\n📊 测试总结:');
    console.log('✅ 错误处理器工作正常');
    console.log('✅ 安全文件系统工作正常');
    console.log('✅ 控制台拦截工作正常');
    console.log('✅ 文件系统错误被成功处理');
    console.log('✅ 系统在错误情况下仍能正常运行');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests()
    .then(() => {
      console.log('\n✨ 错误处理修复验证完成！');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 测试运行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  testErrorHandler,
  testSafeFileSystem,
  testConsoleInterception,
  simulateRealUsage,
  runAllTests
};
