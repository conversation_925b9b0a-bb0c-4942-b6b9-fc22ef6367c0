// 数据分析页面
const app = getApp();

Page({
  data: {
    // 统计数据
    statistics: {
      totalSteps: 0,
      totalDistance: 0,
      accuracy: 0.85,
      trackingTime: 0,
      averageStepLength: 0.75,
      matchRate: 0,
      correctionRate: 0,
      averageProcessingTime: 0
    },

    // 当前传感器数据
    currentSensorData: {
      accelerometer: { magnitude: 0 },
      gyroscope: { magnitude: 0 },
      compass: { direction: 0 }
    },

    // 传感器状态
    sensorStatus: {
      accelerometer: 'inactive',
      gyroscope: 'inactive',
      compass: 'inactive'
    },

    // 轨迹数据
    trajectoryData: [],
    trajectoryBounds: { width: 0, height: 0 },
    trajectoryView: '2d',

    // MLA匹配历史
    mlaHistory: [],

    // 当前位置和状态
    currentPosition: { x: 0, y: 0, z: 0 },
    currentHeading: 0,
    currentFloor: 4,

    // 算法参数
    fusionParams: {
      pdrWeight: 0.7,
      mlaWeight: 0.3,
      confidenceThreshold: 0.6
    },

    // UI状态
    showDetails: false,
    isLoading: false,
    loadingText: '加载中...',
    showToast: false,
    toastMessage: ''
  },

  // 数据更新定时器
  updateTimer: null,
  canvasContext: null,

  onLoad: function (options) {
    console.log('数据分析页面加载');
    this.initializePage();
  },

  onShow: function () {
    this.startDataUpdate();
    this.setupCanvas();
  },

  onHide: function () {
    this.stopDataUpdate();
  },

  onUnload: function () {
    this.cleanup();
  },

  /**
   * 初始化页面
   */
  initializePage() {
    this.loadStoredData();
    this.loadCurrentData();
    this.setupCanvas();
  },

  /**
   * 加载存储的数据
   */
  loadStoredData() {
    try {
      // 加载MLA匹配历史
      const mlaHistory = wx.getStorageSync('mlaMatchHistory') || [];
      const formattedMlaHistory = mlaHistory.slice(0, 20).map(item => ({
        ...item,
        formattedTime: this.formatTimestamp(item.timestamp),
        confidence: (item.confidence * 100).toFixed(1)
      }));
      this.setData({ mlaHistory: formattedMlaHistory });

      // 加载轨迹数据
      const trajectoryData = app.globalData.trackingPath || [];
      this.setData({ trajectoryData: trajectoryData });

      this.calculateTrajectoryBounds();
    } catch (error) {
      console.error('加载存储数据失败:', error);
    }
  },

  /**
   * 加载当前数据
   */
  loadCurrentData() {
    // 从全局数据获取当前状态
    const currentPosition = app.globalData.currentPosition || { x: 0, y: 0, z: 0 };
    const fusionParams = app.globalData.config || {};

    this.setData({
      currentPosition: {
        x: (currentPosition.x || 0).toFixed(2),
        y: (currentPosition.y || 0).toFixed(2),
        z: (currentPosition.z || 0).toFixed(2)
      },
      currentHeading: (currentPosition.heading || 0).toFixed(1),
      currentFloor: currentPosition.floor || 4,
      fusionParams: {
        pdrWeight: (fusionParams.pdrWeight || 0.7).toFixed(2),
        mlaWeight: (fusionParams.mlaWeight || 0.3).toFixed(2),
        confidenceThreshold: (fusionParams.confidenceThreshold || 0.6).toFixed(2)
      }
    });

    // 获取PDR引擎统计数据
    if (app.globalData.pdrEngine) {
      const stats = app.globalData.pdrEngine.getStatistics();
      this.updateStatistics(stats);
    }
  },

  /**
   * 开始数据更新
   */
  startDataUpdate() {
    this.updateTimer = setInterval(() => {
      this.refreshData();
    }, 1000); // 每秒更新一次
  },

  /**
   * 停止数据更新
   */
  stopDataUpdate() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }
  },

  /**
   * 刷新数据
   */
  refreshData() {
    // 更新传感器状态
    this.updateSensorStatus();
    
    // 更新统计数据
    this.loadCurrentData();
    
    // 更新轨迹数据
    const trajectoryData = app.globalData.trackingPath || [];
    if (trajectoryData.length !== this.data.trajectoryData.length) {
      this.setData({ trajectoryData: trajectoryData });
      this.calculateTrajectoryBounds();
      this.drawTrajectory();
    }

    // 更新传感器数据显示
    this.updateSensorDataDisplay();
  },

  /**
   * 更新统计数据
   */
  updateStatistics(stats) {
    this.setData({
      statistics: {
        totalSteps: stats.totalSteps || 0,
        totalDistance: parseFloat((stats.totalDistance || 0).toFixed(2)),
        accuracy: '85.0', // 模拟精度值
        trackingTime: this.calculateTrackingTime(),
        trackingTimeFormatted: this.formatTime(this.calculateTrackingTime()),
        averageStepLength: parseFloat((stats.averageStepLength || 0.75).toFixed(2)),
        stepLengthPercentage: ((stats.averageStepLength || 0.75) / 1.0 * 100).toFixed(0),
        matchRate: ((stats.matchRate || 0) * 100).toFixed(1),
        correctionRate: ((stats.correctionRate || 0) * 100).toFixed(1),
        averageProcessingTime: parseFloat((stats.averageProcessingTime || 0).toFixed(1)),
        processingTimePercentage: Math.min((stats.averageProcessingTime || 0) / 50 * 100, 100).toFixed(0)
      }
    });
  },

  /**
   * 计算跟踪时间
   */
  calculateTrackingTime() {
    const trajectoryData = this.data.trajectoryData;
    if (trajectoryData.length < 2) return 0;
    
    const startTime = trajectoryData[0].timestamp;
    const endTime = trajectoryData[trajectoryData.length - 1].timestamp;
    
    return endTime - startTime;
  },

  /**
   * 更新传感器状态
   */
  updateSensorStatus() {
    const isTracking = app.globalData.isTracking || false;
    
    this.setData({
      sensorStatus: {
        accelerometer: isTracking ? 'active' : 'inactive',
        gyroscope: isTracking ? 'active' : 'inactive',
        compass: isTracking ? 'active' : 'inactive'
      }
    });
  },

  /**
   * 更新传感器数据显示
   */
  updateSensorDataDisplay() {
    // 模拟传感器数据更新
    const currentSensorData = {
      accelerometer: {
        magnitude: (Math.random() * 5 + 9).toFixed(2) // 9-14 m/s²
      },
      gyroscope: {
        magnitude: (Math.random() * 2).toFixed(2) // 0-2 rad/s
      },
      compass: {
        direction: (Math.random() * 360).toFixed(1) // 0-360°
      }
    };

    this.setData({ currentSensorData: currentSensorData });
  },

  /**
   * 设置画布
   */
  setupCanvas() {
    wx.getSystemInfo({
      success: (res) => {
        this.canvasContext = wx.createCanvasContext('trajectoryCanvas', this);
        this.drawTrajectory();
        
        // 设置传感器图表画布
        this.setupSensorCharts();
      }
    });
  },

  /**
   * 设置传感器图表
   */
  setupSensorCharts() {
    // 加速计图表
    const accCtx = wx.createCanvasContext('accChart', this);
    this.drawSensorChart(accCtx, 'accelerometer');

    // 陀螺仪图表
    const gyroCtx = wx.createCanvasContext('gyroChart', this);
    this.drawSensorChart(gyroCtx, 'gyroscope');
  },

  /**
   * 绘制传感器图表
   */
  drawSensorChart(ctx, sensorType) {
    const width = 100;
    const height = 60;
    
    // 清空画布
    ctx.clearRect(0, 0, width, height);
    ctx.setFillStyle('#f8f9fa');
    ctx.fillRect(0, 0, width, height);
    
    // 绘制模拟波形
    ctx.setStrokeStyle('#2196F3');
    ctx.setLineWidth(2);
    ctx.beginPath();
    
    for (let x = 0; x < width; x += 2) {
      const y = height/2 + Math.sin(x * 0.1 + Date.now() * 0.01) * 20 * Math.random();
      if (x === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    
    ctx.stroke();
    ctx.draw();
  },

  /**
   * 绘制轨迹
   */
  drawTrajectory() {
    const ctx = this.canvasContext;
    if (!ctx) return;

    const width = 300;
    const height = 200;
    const trajectory = this.data.trajectoryData;

    // 清空画布
    ctx.clearRect(0, 0, width, height);
    ctx.setFillStyle('#f8f9fa');
    ctx.fillRect(0, 0, width, height);

    if (trajectory.length === 0) {
      // 显示空状态
      ctx.setFillStyle('#999');
      ctx.setFontSize(12);
      ctx.fillText('暂无轨迹数据', width/2 - 40, height/2);
      ctx.draw();
      return;
    }

    // 计算缩放参数
    const bounds = this.data.trajectoryBounds;
    const scale = Math.min(
      (width - 40) / Math.max(bounds.width, 1),
      (height - 40) / Math.max(bounds.height, 1)
    );

    // 绘制轨迹路径
    ctx.setStrokeStyle('#2196F3');
    ctx.setLineWidth(2);
    ctx.beginPath();
    
    trajectory.forEach((point, index) => {
      const x = (point.x - bounds.minX) * scale + 20;
      const y = height - ((point.y - bounds.minY) * scale + 20);
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();

    // 绘制起点和终点
    if (trajectory.length > 0) {
      const start = trajectory[0];
      const startX = (start.x - bounds.minX) * scale + 20;
      const startY = height - ((start.y - bounds.minY) * scale + 20);
      
      ctx.setFillStyle('#4CAF50');
      ctx.beginPath();
      ctx.arc(startX, startY, 4, 0, 2 * Math.PI);
      ctx.fill();
      
      if (trajectory.length > 1) {
        const end = trajectory[trajectory.length - 1];
        const endX = (end.x - bounds.minX) * scale + 20;
        const endY = height - ((end.y - bounds.minY) * scale + 20);
        
        ctx.setFillStyle('#F44336');
        ctx.beginPath();
        ctx.arc(endX, endY, 4, 0, 2 * Math.PI);
        ctx.fill();
      }
    }

    ctx.draw();
  },

  /**
   * 计算轨迹边界
   */
  calculateTrajectoryBounds() {
    const trajectory = this.data.trajectoryData;
    
    if (trajectory.length === 0) {
      this.setData({
        trajectoryBounds: { width: 0, height: 0, minX: 0, minY: 0, maxX: 0, maxY: 0 }
      });
      return;
    }
    
    let minX = trajectory[0].x;
    let maxX = trajectory[0].x;
    let minY = trajectory[0].y;
    let maxY = trajectory[0].y;
    
    trajectory.forEach(point => {
      minX = Math.min(minX, point.x);
      maxX = Math.max(maxX, point.x);
      minY = Math.min(minY, point.y);
      maxY = Math.max(maxY, point.y);
    });
    
    this.setData({
      trajectoryBounds: {
        width: (maxX - minX).toFixed(1),
        height: (maxY - minY).toFixed(1),
        minX: minX,
        minY: minY,
        maxX: maxX,
        maxY: maxY
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(ms) {
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  },

  /**
   * 格式化时间戳
   */
  formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffMinutes < 1) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffMinutes < 1440) {
      return `${Math.floor(diffMinutes / 60)}小时前`;
    } else {
      return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }
  },

  /**
   * 切换轨迹视图
   */
  setTrajectoryView(e) {
    const view = e.currentTarget.dataset.view;
    this.setData({ trajectoryView: view });
    
    if (view === '2d') {
      this.drawTrajectory();
    }
  },

  /**
   * 刷新数据
   */
  refreshData() {
    this.setData({ 
      isLoading: true, 
      loadingText: '刷新数据中...' 
    });
    
    setTimeout(() => {
      this.loadCurrentData();
      this.loadStoredData();
      this.drawTrajectory();
      
      this.setData({ isLoading: false });
      this.showToast('数据已刷新');
    }, 1000);
  },

  /**
   * 导出数据
   */
  exportData() {
    const exportData = {
      statistics: this.data.statistics,
      trajectoryData: this.data.trajectoryData,
      mlaHistory: this.data.mlaHistory,
      currentState: {
        position: this.data.currentPosition,
        heading: this.data.currentHeading,
        floor: this.data.currentFloor
      },
      fusionParams: this.data.fusionParams,
      exportTime: new Date().toISOString()
    };
    
    const jsonData = JSON.stringify(exportData, null, 2);
    
    wx.setClipboardData({
      data: jsonData,
      success: () => {
        this.showToast('数据已复制到剪贴板');
      },
      fail: () => {
        this.showToast('导出失败');
      }
    });
  },

  /**
   * 分享数据
   */
  shareData() {
    // 生成分享内容
    const stats = this.data.statistics;
    const shareText = `室内定位数据报告
总步数: ${stats.totalSteps}
总距离: ${stats.totalDistance}m  
定位精度: ${(stats.accuracy * 100).toFixed(1)}%
跟踪时间: ${this.formatTime(stats.trackingTime)}
平均步长: ${stats.averageStepLength}m`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        this.showToast('分享内容已复制');
      }
    });
  },

  /**
   * 清空MLA历史
   */
  clearMlaHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空MLA匹配历史吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ mlaHistory: [] });
          wx.removeStorageSync('mlaMatchHistory');
          this.showToast('MLA历史已清空');
        }
      }
    });
  },

  /**
   * 清空所有数据
   */
  clearAllData() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有数据吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          // 清空存储数据
          wx.removeStorageSync('trajectoryHistory');
          wx.removeStorageSync('mlaMatchHistory');
          
          // 重置页面数据
          this.setData({
            trajectoryData: [],
            mlaHistory: [],
            statistics: {
              totalSteps: 0,
              totalDistance: 0,
              accuracy: 0.85,
              trackingTime: 0,
              averageStepLength: 0.75,
              matchRate: 0,
              correctionRate: 0,
              averageProcessingTime: 0
            }
          });
          
          this.calculateTrajectoryBounds();
          this.drawTrajectory();
          this.showToast('所有数据已清空');
        }
      }
    });
  },

  /**
   * 画布触摸事件
   */
  onCanvasTouchStart(e) {
    this.touchStartX = e.touches[0].x;
    this.touchStartY = e.touches[0].y;
  },

  onCanvasTouchMove(e) {
    // 简单的拖拽实现
    if (this.touchStartX && this.touchStartY) {
      // 可以在这里实现轨迹画布的拖拽功能
      this.touchStartX = e.touches[0].x;
      this.touchStartY = e.touches[0].y;
    }
  },

  /**
   * 显示/隐藏详情
   */
  showDetails() {
    this.setData({ showDetails: true });
  },

  hideDetails() {
    this.setData({ showDetails: false });
  },

  /**
   * 显示提示信息
   */
  showToast(message) {
    this.setData({
      showToast: true,
      toastMessage: message
    });
    
    setTimeout(() => {
      this.setData({ showToast: false });
    }, 2000);
  },

  hideToast() {
    this.setData({ showToast: false });
  },

  /**
   * 清理资源
   */
  cleanup() {
    this.stopDataUpdate();
    console.log('数据分析页面资源已清理');
  }
});