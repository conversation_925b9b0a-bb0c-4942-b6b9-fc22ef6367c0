// 轨迹分析页面
const app = getApp();

Page({
  data: {
    // 轨迹数据
    trajectoryData: [],
    selectedTrajectory: null,
    
    // 显示模式
    viewMode: '2d', // 2d, 3d, list
    showDetails: false,
    
    // 统计信息
    totalDistance: 0,
    totalTime: 0,
    averageSpeed: 0,
    maxSpeed: 0,
    
    // 筛选条件
    dateFilter: 'all', // all, today, week, month
    floorFilter: 'all', // all, 4, 5, etc.
    
    // UI状态
    isLoading: false,
    loadingText: '加载中...',
    showToast: false,
    toastMessage: '',
    
    // 画布参数
    canvasWidth: 0,
    canvasHeight: 0,
    scale: 10,
    offsetX: 0,
    offsetY: 0
  },

  canvasContext: null,

  onLoad: function (options) {
    console.log('轨迹分析页面加载');
    this.setupCanvas();
    this.loadTrajectoryData();
  },

  onShow: function () {
    this.refreshTrajectoryData();
  },

  /**
   * 设置画布
   */
  setupCanvas() {
    wx.getSystemInfo({
      success: (res) => {
        const pixelRatio = res.pixelRatio || 2;
        this.setData({
          canvasWidth: res.windowWidth * pixelRatio,
          canvasHeight: res.windowHeight * 0.6 * pixelRatio
        });
        
        this.canvasContext = wx.createCanvasContext('trajectoryCanvas', this);
        this.canvasContext.scale(2, 2);
        this.drawTrajectory();
      }
    });
  },

  /**
   * 加载轨迹数据
   */
  loadTrajectoryData() {
    this.setData({ isLoading: true, loadingText: '加载轨迹数据...' });
    
    try {
      // 从本地存储加载轨迹数据
      const trajectoryData = wx.getStorageSync('trajectoryHistory') || [];
      
      // 从全局数据获取当前轨迹
      const currentTrajectory = app.globalData.trackingPath || [];
      
      // 合并数据
      let allTrajectories = [...trajectoryData];
      if (currentTrajectory.length > 0) {
        allTrajectories.unshift({
          id: 'current',
          name: '当前轨迹',
          timestamp: Date.now(),
          data: currentTrajectory,
          distance: this.calculateDistance(currentTrajectory),
          duration: this.calculateDuration(currentTrajectory),
          floor: app.globalData.currentPosition.floor || 4
        });
      }
      
      // 格式化轨迹数据
      allTrajectories = allTrajectories.map(trajectory => ({
        ...trajectory,
        durationFormatted: this.formatTime(trajectory.duration),
        timestampFormatted: this.formatDate(trajectory.timestamp)
      }));
      
      this.setData({
        trajectoryData: allTrajectories,
        selectedTrajectory: allTrajectories[0] || null
      });
      
      this.calculateStatistics();
      this.drawTrajectory();
      
    } catch (error) {
      console.error('加载轨迹数据失败:', error);
      this.showToast('加载轨迹数据失败');
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 刷新轨迹数据
   */
  refreshTrajectoryData() {
    this.loadTrajectoryData();
  },

  /**
   * 计算统计信息
   */
  calculateStatistics() {
    const { trajectoryData } = this.data;
    
    if (trajectoryData.length === 0) {
      return;
    }
    
    let totalDistance = 0;
    let totalTime = 0;
    let maxSpeed = 0;
    
    trajectoryData.forEach(trajectory => {
      totalDistance += trajectory.distance || 0;
      totalTime += trajectory.duration || 0;
      
      // 计算最大速度
      const points = trajectory.data || [];
      for (let i = 1; i < points.length; i++) {
        const timeDiff = (points[i].timestamp - points[i-1].timestamp) / 1000; // 秒
        const distance = this.getPointDistance(points[i-1], points[i]);
        const speed = distance / timeDiff;
        
        if (speed > maxSpeed) {
          maxSpeed = speed;
        }
      }
    });
    
    const averageSpeed = totalTime > 0 ? totalDistance / (totalTime / 1000) : 0;
    
    this.setData({
      totalDistance: totalDistance.toFixed(2),
      totalTime: totalTime,
      totalTimeFormatted: this.formatTime(totalTime),
      averageSpeed: averageSpeed.toFixed(2),
      maxSpeed: maxSpeed.toFixed(2)
    });
  },

  /**
   * 计算轨迹总距离
   */
  calculateDistance(trajectory) {
    if (!trajectory || trajectory.length < 2) return 0;
    
    let distance = 0;
    for (let i = 1; i < trajectory.length; i++) {
      distance += this.getPointDistance(trajectory[i-1], trajectory[i]);
    }
    
    return distance;
  },

  /**
   * 计算轨迹总时长
   */
  calculateDuration(trajectory) {
    if (!trajectory || trajectory.length < 2) return 0;
    
    const startTime = trajectory[0].timestamp;
    const endTime = trajectory[trajectory.length - 1].timestamp;
    
    return endTime - startTime;
  },

  /**
   * 计算两点间距离
   */
  getPointDistance(p1, p2) {
    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;
    return Math.sqrt(dx * dx + dy * dy);
  },

  /**
   * 绘制轨迹
   */
  drawTrajectory() {
    const ctx = this.canvasContext;
    if (!ctx) return;

    const width = this.data.canvasWidth / 2;
    const height = this.data.canvasHeight / 2;
    const trajectory = this.data.selectedTrajectory;

    // 清空画布
    ctx.clearRect(0, 0, width, height);
    ctx.setFillStyle('#f8f9fa');
    ctx.fillRect(0, 0, width, height);

    if (!trajectory || !trajectory.data || trajectory.data.length === 0) {
      // 显示空状态
      ctx.setFillStyle('#999');
      ctx.setFontSize(16);
      ctx.fillText('暂无轨迹数据', width/2 - 50, height/2);
      ctx.draw();
      return;
    }

    // 绘制网格
    this.drawGrid(ctx, width, height);
    
    // 绘制轨迹路径
    this.drawTrajectoryPath(ctx, width, height, trajectory.data);
    
    // 绘制起点和终点
    this.drawStartEndPoints(ctx, width, height, trajectory.data);

    ctx.draw();
  },

  /**
   * 绘制网格
   */
  drawGrid(ctx, width, height) {
    const scale = this.data.scale;
    const offsetX = this.data.offsetX;
    const offsetY = this.data.offsetY;
    
    ctx.setStrokeStyle('#e0e0e0');
    ctx.setLineWidth(0.5);
    
    // 绘制垂直线
    for (let x = offsetX % (scale * 5); x < width; x += scale * 5) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
    
    // 绘制水平线
    for (let y = offsetY % (scale * 5); y < height; y += scale * 5) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }

    // 绘制坐标轴
    const centerX = width / 2 + offsetX;
    const centerY = height / 2 + offsetY;
    
    if (centerX >= 0 && centerX <= width) {
      ctx.setStrokeStyle('#4CAF50');
      ctx.setLineWidth(2);
      ctx.beginPath();
      ctx.moveTo(centerX, 0);
      ctx.lineTo(centerX, height);
      ctx.stroke();
    }
    
    if (centerY >= 0 && centerY <= height) {
      ctx.setStrokeStyle('#2196F3');
      ctx.setLineWidth(2);
      ctx.beginPath();
      ctx.moveTo(0, centerY);
      ctx.lineTo(width, centerY);
      ctx.stroke();
    }
  },

  /**
   * 绘制轨迹路径
   */
  drawTrajectoryPath(ctx, width, height, trajectory) {
    if (trajectory.length < 2) return;

    ctx.setStrokeStyle('#2196F3');
    ctx.setLineWidth(3);
    ctx.beginPath();
    
    const startPos = this.worldToScreen(trajectory[0].x, trajectory[0].y, width, height);
    ctx.moveTo(startPos.x, startPos.y);
    
    for (let i = 1; i < trajectory.length; i++) {
      const pos = this.worldToScreen(trajectory[i].x, trajectory[i].y, width, height);
      ctx.lineTo(pos.x, pos.y);
    }
    
    ctx.stroke();

    // 绘制轨迹点
    ctx.setFillStyle('#1976D2');
    trajectory.forEach((point, index) => {
      const screenPos = this.worldToScreen(point.x, point.y, width, height);
      ctx.beginPath();
      
      // 根据点的类型调整大小
      const radius = point.corrected ? 4 : 2;
      ctx.arc(screenPos.x, screenPos.y, radius, 0, 2 * Math.PI);
      ctx.fill();
      
      // 如果是校正点，添加特殊标记
      if (point.corrected) {
        ctx.setStrokeStyle('#FF9800');
        ctx.setLineWidth(2);
        ctx.beginPath();
        ctx.arc(screenPos.x, screenPos.y, radius + 2, 0, 2 * Math.PI);
        ctx.stroke();
      }
    });
  },

  /**
   * 绘制起点和终点
   */
  drawStartEndPoints(ctx, width, height, trajectory) {
    if (trajectory.length === 0) return;

    // 起点
    const startPos = this.worldToScreen(trajectory[0].x, trajectory[0].y, width, height);
    ctx.setFillStyle('#4CAF50');
    ctx.beginPath();
    ctx.arc(startPos.x, startPos.y, 8, 0, 2 * Math.PI);
    ctx.fill();
    
    ctx.setFillStyle('#fff');
    ctx.setFontSize(12);
    ctx.fillText('起', startPos.x - 6, startPos.y + 4);

    // 终点
    if (trajectory.length > 1) {
      const endPos = this.worldToScreen(
        trajectory[trajectory.length - 1].x, 
        trajectory[trajectory.length - 1].y, 
        width, height
      );
      ctx.setFillStyle('#F44336');
      ctx.beginPath();
      ctx.arc(endPos.x, endPos.y, 8, 0, 2 * Math.PI);
      ctx.fill();
      
      ctx.setFillStyle('#fff');
      ctx.setFontSize(12);
      ctx.fillText('终', endPos.x - 6, endPos.y + 4);
    }
  },

  /**
   * 世界坐标转屏幕坐标
   */
  worldToScreen(worldX, worldY, screenWidth, screenHeight) {
    const scale = this.data.scale;
    const offsetX = this.data.offsetX;
    const offsetY = this.data.offsetY;
    
    return {
      x: (worldX * scale) + (screenWidth / 2) + offsetX,
      y: (screenHeight / 2) - (worldY * scale) + offsetY
    };
  },

  /**
   * 切换显示模式
   */
  setViewMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({ viewMode: mode });
    
    if (mode === '2d') {
      this.drawTrajectory();
    }
  },

  /**
   * 选择轨迹
   */
  selectTrajectory(e) {
    const index = e.currentTarget.dataset.index;
    const trajectory = this.data.trajectoryData[index];
    
    this.setData({ selectedTrajectory: trajectory });
    this.drawTrajectory();
  },

  /**
   * 删除轨迹
   */
  deleteTrajectory(e) {
    const index = e.currentTarget.dataset.index;
    const trajectory = this.data.trajectoryData[index];
    
    if (trajectory.id === 'current') {
      this.showToast('无法删除当前轨迹');
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除轨迹 "${trajectory.name}" 吗？`,
      success: (res) => {
        if (res.confirm) {
          const newData = this.data.trajectoryData.filter((_, i) => i !== index);
          this.setData({ trajectoryData: newData });
          
          // 更新本地存储
          const storageData = newData.filter(t => t.id !== 'current');
          wx.setStorageSync('trajectoryHistory', storageData);
          
          // 如果删除的是当前选中的轨迹，选择第一个
          if (this.data.selectedTrajectory === trajectory) {
            this.setData({ selectedTrajectory: newData[0] || null });
          }
          
          this.calculateStatistics();
          this.drawTrajectory();
          this.showToast('轨迹已删除');
        }
      }
    });
  },

  /**
   * 保存当前轨迹
   */
  saveCurrentTrajectory() {
    const currentTrajectory = app.globalData.trackingPath;
    
    if (!currentTrajectory || currentTrajectory.length === 0) {
      this.showToast('当前没有轨迹数据');
      return;
    }
    
    wx.showModal({
      title: '保存轨迹',
      editable: true,
      placeholderText: '请输入轨迹名称',
      success: (res) => {
        if (res.confirm) {
          const name = res.content || `轨迹_${new Date().toLocaleDateString()}`;
          
          const trajectoryRecord = {
            id: Date.now().toString(),
            name: name,
            timestamp: Date.now(),
            data: [...currentTrajectory],
            distance: this.calculateDistance(currentTrajectory),
            duration: this.calculateDuration(currentTrajectory),
            floor: app.globalData.currentPosition.floor || 4
          };
          
          // 保存到本地存储
          try {
            const existingData = wx.getStorageSync('trajectoryHistory') || [];
            existingData.unshift(trajectoryRecord);
            
            // 限制保存数量
            if (existingData.length > 50) {
              existingData.splice(50);
            }
            
            wx.setStorageSync('trajectoryHistory', existingData);
            
            this.showToast('轨迹保存成功');
            this.loadTrajectoryData();
          } catch (error) {
            console.error('保存轨迹失败:', error);
            this.showToast('保存失败');
          }
        }
      }
    });
  },

  /**
   * 导出轨迹数据
   */
  exportTrajectory() {
    const trajectory = this.data.selectedTrajectory;
    
    if (!trajectory) {
      this.showToast('请选择要导出的轨迹');
      return;
    }
    
    const exportData = {
      name: trajectory.name,
      timestamp: trajectory.timestamp,
      distance: trajectory.distance,
      duration: trajectory.duration,
      floor: trajectory.floor,
      points: trajectory.data.map(point => ({
        x: point.x,
        y: point.y,
        z: point.z,
        timestamp: point.timestamp,
        heading: point.heading,
        stepCount: point.stepCount,
        corrected: point.corrected || false
      }))
    };
    
    const jsonData = JSON.stringify(exportData, null, 2);
    
    // 复制到剪贴板
    wx.setClipboardData({
      data: jsonData,
      success: () => {
        this.showToast('轨迹数据已复制到剪贴板');
      },
      fail: () => {
        this.showToast('导出失败');
      }
    });
  },

  /**
   * 清空所有轨迹
   */
  clearAllTrajectories() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有保存的轨迹吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('trajectoryHistory');
            this.setData({
              trajectoryData: [],
              selectedTrajectory: null
            });
            this.calculateStatistics();
            this.drawTrajectory();
            this.showToast('所有轨迹已清空');
          } catch (error) {
            console.error('清空轨迹失败:', error);
            this.showToast('清空失败');
          }
        }
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
    }
  },

  /**
   * 格式化日期
   */
  formatDate(timestamp) {
    const date = new Date(timestamp);
    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  },

  /**
   * 画布触摸事件
   */
  onCanvasTouchStart(e) {
    this.touchStartX = e.touches[0].x;
    this.touchStartY = e.touches[0].y;
  },

  onCanvasTouchMove(e) {
    if (this.touchStartX && this.touchStartY) {
      const deltaX = e.touches[0].x - this.touchStartX;
      const deltaY = e.touches[0].y - this.touchStartY;
      
      this.setData({
        offsetX: this.data.offsetX + deltaX,
        offsetY: this.data.offsetY + deltaY
      });
      
      this.drawTrajectory();
      
      this.touchStartX = e.touches[0].x;
      this.touchStartY = e.touches[0].y;
    }
  },

  onCanvasTouchEnd(e) {
    this.touchStartX = null;
    this.touchStartY = null;
  },

  /**
   * 重置视图
   */
  resetView() {
    this.setData({
      scale: 10,
      offsetX: 0,
      offsetY: 0
    });
    this.drawTrajectory();
  },

  /**
   * 居中显示轨迹
   */
  centerTrajectory() {
    const trajectory = this.data.selectedTrajectory;
    
    if (!trajectory || !trajectory.data || trajectory.data.length === 0) {
      return;
    }
    
    // 计算轨迹边界
    let minX = trajectory.data[0].x;
    let maxX = trajectory.data[0].x;
    let minY = trajectory.data[0].y;
    let maxY = trajectory.data[0].y;
    
    trajectory.data.forEach(point => {
      minX = Math.min(minX, point.x);
      maxX = Math.max(maxX, point.x);
      minY = Math.min(minY, point.y);
      maxY = Math.max(maxY, point.y);
    });
    
    // 计算中心点
    const centerX = (minX + maxX) / 2;
    const centerY = (minY + maxY) / 2;
    
    // 计算合适的缩放比例
    const width = this.data.canvasWidth / 2;
    const height = this.data.canvasHeight / 2;
    const rangeX = maxX - minX;
    const rangeY = maxY - minY;
    
    let scale = Math.min(width / (rangeX * 1.2), height / (rangeY * 1.2));
    scale = Math.max(1, Math.min(50, scale)); // 限制缩放范围
    
    this.setData({
      scale: scale,
      offsetX: -centerX * scale,
      offsetY: centerY * scale
    });
    
    this.drawTrajectory();
  },

  /**
   * 显示详情
   */
  showDetails() {
    this.setData({ showDetails: true });
  },

  hideDetails() {
    this.setData({ showDetails: false });
  },

  /**
   * 显示提示信息
   */
  showToast(message) {
    this.setData({
      showToast: true,
      toastMessage: message
    });
    
    setTimeout(() => {
      this.setData({ showToast: false });
    }, 2000);
  },

  hideToast() {
    this.setData({ showToast: false });
  }
});