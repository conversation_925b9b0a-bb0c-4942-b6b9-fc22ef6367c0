plugins {
    id 'com.android.application'
    id 'jar-gradle-plugin'
}

android {
    namespace 'com.example.pdrdemo'
    compileSdk 34
    
    defaultConfig {
        applicationId "com.example.pdrdemo"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    buildFeatures {
        viewBinding true
    }


    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    implementation files('libs/evbus.jar')
    implementation files('libs/loclore.jar')
    implementation 'com.orhanobut:logger:2.2.0'
    //添加日志输出保存到本地模块
    implementation files('libs/Mode.jar')
    implementation files('libs/Sensors.jar')
    implementation files('libs/libandroid_tensorflow_inference_java.jar')
    implementation files('libs/KF.jar')
    implementation files('libs/HA.jar')
    //数据库+EXCEL
    implementation files('libs/xUtils-2.6.14.jar')
    implementation files('libs/poi-3.14-20160307.jar')
    //连接postresql用的JDBC
//    implementation 'org.postgresql:postgresql:9.4.1212'
//    implementation files('libs/postgresql-9.4.1212.jar')
}

BuildJar{
    //输出目录
    outputFileDir= project.buildDir.path+"/jar"
    //输出原始jar包名
    outputFileName="PDR.jar"
    //输出混淆jar包名
    outputProguardFileName="pdr_Mix.jar"
    //混淆配置
    proguardConfigFile="proguard-rules.pro"
    //是否需要默认的混淆配置proguard-android.txt
    needDefaultProguard=true
    applyMappingFile="originMapping/mapping.txt"
    //需要输出jar的包名列表,当此参数为空时，则默认全项目输出,支持多包,如 includePackage=['com/adison/testjarplugin/include','com/adison/testjarplugin/include1'...]
    includePackage=['com/PDR']
    includeClass=[]
//    includePackage=['com/adison/testjarplugin/include']
    //不需要输出jar的jar包列表,如['baidu.jar','baidu1.jar'...]
//    excludeJar=[]
//    //不需要输出jar的类名列表,如['baidu.calss','baidu1.class'...]
//    excludeClass=['com/adison/testjarplugin/TestExcude.class']
//    //不需要输出jar的包名列表,如 excludePackage=['com/adison/testjarplugin/exclude','com/adison/testjarplugin/exclude1'...]
//    excludePackage=['com/adison/testjarplugin/exclude']

//    Command:gradlew buildProguardJar
}


