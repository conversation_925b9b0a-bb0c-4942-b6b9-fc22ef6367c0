{"description": "室内惯导微信小程序项目配置", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "compileWorklet": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wx28ae9a89b16056e5", "projectname": "IndoorPDRMiniApp", "isGameTourist": false, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "plugin": {"current": -1, "list": []}, "gamePlugin": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}, "editorSetting": {}}