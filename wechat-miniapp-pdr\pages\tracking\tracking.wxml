<!-- 轨迹分析页面 -->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <text class="title">轨迹分析</text>
    <text class="subtitle">Track Analysis</text>
  </view>

  <!-- 统计信息卡片 -->
  <view class="stats-card">
    <view class="stats-row">
      <view class="stat-item">
        <text class="stat-value">{{totalDistance}}</text>
        <text class="stat-label">总距离(m)</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{totalTimeFormatted}}</text>
        <text class="stat-label">总时间</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{averageSpeed}}</text>
        <text class="stat-label">平均速度(m/s)</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{maxSpeed}}</text>
        <text class="stat-label">最大速度(m/s)</text>
      </view>
    </view>
  </view>

  <!-- 视图模式切换 -->
  <view class="view-selector">
    <button 
      class="view-btn {{viewMode === '2d' ? 'active' : ''}}" 
      data-mode="2d" 
      bind:tap="setViewMode"
    >2D轨迹</button>
    <button 
      class="view-btn {{viewMode === 'list' ? 'active' : ''}}" 
      data-mode="list" 
      bind:tap="setViewMode"
    >列表</button>
  </view>

  <!-- 轨迹可视化 -->
  <view class="trajectory-container" wx:if="{{viewMode === '2d'}}">
    <canvas 
      canvas-id="trajectoryCanvas"
      class="trajectory-canvas"
      bind:touchstart="onCanvasTouchStart"
      bind:touchmove="onCanvasTouchMove"
      bind:touchend="onCanvasTouchEnd"
    ></canvas>
    
    <!-- 画布控制按钮 -->
    <view class="canvas-controls">
      <button class="control-btn" bind:tap="resetView">重置</button>
      <button class="control-btn" bind:tap="centerTrajectory">居中</button>
    </view>
  </view>

  <!-- 轨迹列表 -->
  <view class="trajectory-list" wx:if="{{viewMode === 'list'}}">
    <view wx:if="{{trajectoryData.length === 0}}" class="empty-state">
      <text class="empty-text">暂无轨迹数据</text>
    </view>
    
    <view 
      wx:for="{{trajectoryData}}" 
      wx:key="id" 
      class="trajectory-item {{selectedTrajectory && selectedTrajectory.id === item.id ? 'selected' : ''}}"
      data-index="{{index}}"
      bind:tap="selectTrajectory"
    >
      <view class="item-header">
        <text class="item-name">{{item.name}}</text>
        <view class="item-actions">
          <button 
            class="action-icon" 
            data-index="{{index}}" 
            bind:tap="deleteTrajectory"
            wx:if="{{item.id !== 'current'}}"
          >🗑️</button>
        </view>
      </view>
      
      <view class="item-info">
        <text class="info-text">距离: {{item.distance}}m</text>
        <text class="info-text">时长: {{item.durationFormatted}}</text>
        <text class="info-text">楼层: {{item.floor}}F</text>
        <text class="info-text">时间: {{item.timestampFormatted}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="action-button primary" bind:tap="saveCurrentTrajectory">保存轨迹</button>
    <button class="action-button secondary" bind:tap="exportTrajectory">导出数据</button>
    <button class="action-button secondary" bind:tap="clearAllTrajectories">清空轨迹</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <text class="loading-text">{{loadingText}}</text>
      <view class="loading-dots">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
    </view>
  </view>

  <!-- 提示信息 -->
  <view class="toast {{showToast ? 'show' : ''}}" bind:tap="hideToast">
    <text>{{toastMessage}}</text>
  </view>
</view>