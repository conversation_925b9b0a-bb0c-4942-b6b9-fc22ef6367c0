/**
 * 航向角估计器
 * 实现卡尔曼滤波(KF)和扩展卡尔曼滤波(EKF)算法
 */

class HeadingEstimator {
  constructor() {
    // 状态变量 [航向角, 角速度偏置]
    this.state = {
      heading: 0,      // 航向角 (度)
      gyroBias: 0      // 陀螺仪偏置
    };

    // 卡尔曼滤波参数
    this.kf = {
      // 状态协方差矩阵 P
      P: [
        [1, 0],
        [0, 1]
      ],
      // 过程噪声协方差 Q  
      Q: [
        [0.1, 0],
        [0, 0.01]
      ],
      // 观测噪声协方差 R
      R: 4.0, // 罗盘测量噪声
      // 状态转移矩阵 F
      F: [
        [1, 0],
        [0, 1]  
      ],
      // 观测矩阵 H
      H: [1, 0]
    };

    // EKF特有参数
    this.ekf = {
      enabled: true,
      adaptiveQ: true, // 自适应过程噪声
      innovationThreshold: 30 // 新息门限(度)
    };

    // 传感器数据历史
    this.sensorHistory = {
      gyroscope: [],
      compass: [],
      accelerometer: []
    };

    // 滤波器状态
    this.filterState = {
      initialized: false,
      lastUpdate: 0,
      innovationHistory: []
    };

    // 配置参数
    this.config = {
      useCompass: true,
      useGyroscope: true,
      compassWeight: 0.7,
      gyroWeight: 0.3,
      maxDeltaTime: 100, // 最大时间间隔(ms)
      headingSmoothing: 0.8 // 航向角平滑系数
    };
  }

  /**
   * 更新航向角估计
   * @param {Object} sensorData 传感器数据
   * @param {number} sensorData.gyroZ 陀螺仪Z轴角速度
   * @param {number} sensorData.compassHeading 罗盘航向角
   * @param {number} timestamp 时间戳
   */
  updateHeading(sensorData, timestamp) {
    if (!this.filterState.initialized) {
      this.initializeFilter(sensorData, timestamp);
      return this.state.heading;
    }

    const deltaTime = (timestamp - this.filterState.lastUpdate) / 1000.0; // 转换为秒
    
    // 限制时间间隔
    if (deltaTime > this.config.maxDeltaTime / 1000.0) {
      console.warn('时间间隔过大，跳过更新:', deltaTime);
      return this.state.heading;
    }

    // 预测步骤
    this.predict(sensorData.gyroZ, deltaTime);
    
    // 更新步骤（如果有罗盘数据）
    if (this.config.useCompass && sensorData.compassHeading !== undefined) {
      this.update(sensorData.compassHeading);
    }

    // 保存传感器数据历史
    this.saveSensorHistory(sensorData, timestamp);
    
    this.filterState.lastUpdate = timestamp;
    
    return this.normalizeAngle(this.state.heading);
  }

  /**
   * 初始化滤波器
   */
  initializeFilter(sensorData, timestamp) {
    // 使用罗盘初始化航向角
    if (sensorData.compassHeading !== undefined) {
      this.state.heading = sensorData.compassHeading;
    }
    
    this.state.gyroBias = 0;
    
    // 初始化协方差矩阵
    this.kf.P = [
      [10, 0],
      [0, 1]
    ];
    
    this.filterState.initialized = true;
    this.filterState.lastUpdate = timestamp;
    
    console.log('航向角滤波器初始化完成，初始航向:', this.state.heading);
  }

  /**
   * 预测步骤 (卡尔曼滤波预测)
   */
  predict(gyroZ, deltaTime) {
    if (!gyroZ || deltaTime <= 0) return;

    // 陀螺仪数据预处理 (弧度/秒 转 度/秒)
    const gyroZDegrees = gyroZ * (180 / Math.PI);
    
    // 状态预测: x = F * x + B * u
    const correctedGyro = gyroZDegrees - this.state.gyroBias;
    this.state.heading += correctedGyro * deltaTime;
    // gyroBias保持不变
    
    // 协方差预测: P = F * P * F' + Q
    const F = [
      [1, -deltaTime],
      [0, 1]
    ];
    
    // 自适应过程噪声
    let Q = [...this.kf.Q];
    if (this.ekf.adaptiveQ) {
      const gyroVariance = this.calculateGyroVariance();
      Q[0][0] = Math.max(0.01, gyroVariance * deltaTime * deltaTime);
    }
    
    // P = F * P * F' + Q
    const P = this.kf.P;
    const newP = [
      [
        F[0][0] * P[0][0] * F[0][0] + F[0][1] * P[1][0] * F[0][0] + 
        F[0][0] * P[0][1] * F[0][1] + F[0][1] * P[1][1] * F[0][1] + Q[0][0],
        F[0][0] * P[0][0] * F[1][0] + F[0][1] * P[1][0] * F[1][0] + 
        F[0][0] * P[0][1] * F[1][1] + F[0][1] * P[1][1] * F[1][1] + Q[0][1]
      ],
      [
        F[1][0] * P[0][0] * F[0][0] + F[1][1] * P[1][0] * F[0][0] + 
        F[1][0] * P[0][1] * F[0][1] + F[1][1] * P[1][1] * F[0][1] + Q[1][0],
        F[1][0] * P[0][0] * F[1][0] + F[1][1] * P[1][0] * F[1][0] + 
        F[1][0] * P[0][1] * F[1][1] + F[1][1] * P[1][1] * F[1][1] + Q[1][1]
      ]
    ];
    
    this.kf.P = newP;
  }

  /**
   * 更新步骤 (卡尔曼滤波更新)
   */
  update(compassHeading) {
    if (compassHeading === undefined) return;

    // 计算新息 (innovation)
    const predictedHeading = this.normalizeAngle(this.state.heading);
    let innovation = this.normalizeAngle(compassHeading - predictedHeading);
    
    // 新息验证 - 检测异常观测
    if (Math.abs(innovation) > this.ekf.innovationThreshold) {
      console.warn('罗盘观测异常，新息过大:', innovation);
      return;
    }

    // 新息协方差 S = H * P * H' + R
    const H = this.kf.H;
    const P = this.kf.P;
    const S = H[0] * P[0][0] * H[0] + H[1] * P[1][0] * H[0] + 
              H[0] * P[0][1] * H[1] + H[1] * P[1][1] * H[1] + this.kf.R;

    // 卡尔曼增益 K = P * H' * S^(-1)
    const K = [
      (P[0][0] * H[0] + P[0][1] * H[1]) / S,
      (P[1][0] * H[0] + P[1][1] * H[1]) / S
    ];

    // 状态更新 x = x + K * innovation
    this.state.heading += K[0] * innovation;
    this.state.gyroBias += K[1] * innovation;

    // 协方差更新 P = (I - K * H) * P
    const I_KH = [
      [1 - K[0] * H[0], -K[0] * H[1]],
      [-K[1] * H[0], 1 - K[1] * H[1]]
    ];
    
    const newP = [
      [
        I_KH[0][0] * P[0][0] + I_KH[0][1] * P[1][0],
        I_KH[0][0] * P[0][1] + I_KH[0][1] * P[1][1]
      ],
      [
        I_KH[1][0] * P[0][0] + I_KH[1][1] * P[1][0],
        I_KH[1][0] * P[0][1] + I_KH[1][1] * P[1][1]
      ]
    ];
    
    this.kf.P = newP;

    // 保存新息历史用于自适应调整
    this.filterState.innovationHistory.push(Math.abs(innovation));
    if (this.filterState.innovationHistory.length > 20) {
      this.filterState.innovationHistory.shift();
    }
  }

  /**
   * 计算陀螺仪方差
   */
  calculateGyroVariance() {
    const history = this.sensorHistory.gyroscope;
    if (history.length < 10) return 0.1;

    const recent = history.slice(-10);
    const gyroZ = recent.map(d => d.z);
    
    const mean = gyroZ.reduce((sum, val) => sum + val, 0) / gyroZ.length;
    const variance = gyroZ.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / gyroZ.length;
    
    return Math.max(0.01, Math.min(1.0, variance));
  }

  /**
   * 检测转向
   */
  detectTurn(gyroData, threshold = 1.0) {
    if (!gyroData || !gyroData.z) return false;
    
    const gyroZ = Math.abs(gyroData.z);
    return gyroZ > threshold;
  }

  /**
   * 航向角平滑处理
   */
  smoothHeading(newHeading, alpha = null) {
    if (alpha === null) {
      alpha = this.config.headingSmoothing;
    }
    
    const currentHeading = this.state.heading;
    let diff = this.normalizeAngle(newHeading - currentHeading);
    
    // 平滑更新
    this.state.heading = this.normalizeAngle(currentHeading + alpha * diff);
    
    return this.state.heading;
  }

  /**
   * 角度归一化到[-180, 180]
   */
  normalizeAngle(angle) {
    while (angle > 180) angle -= 360;
    while (angle < -180) angle += 360;
    return angle;
  }

  /**
   * 保存传感器数据历史
   */
  saveSensorHistory(sensorData, timestamp) {
    const maxHistory = 50;
    
    if (sensorData.gyroZ !== undefined) {
      this.sensorHistory.gyroscope.push({
        z: sensorData.gyroZ,
        timestamp: timestamp
      });
      if (this.sensorHistory.gyroscope.length > maxHistory) {
        this.sensorHistory.gyroscope.shift();
      }
    }
    
    if (sensorData.compassHeading !== undefined) {
      this.sensorHistory.compass.push({
        heading: sensorData.compassHeading,
        timestamp: timestamp
      });
      if (this.sensorHistory.compass.length > maxHistory) {
        this.sensorHistory.compass.shift();
      }
    }
  }

  /**
   * 获取当前航向角
   */
  getHeading() {
    return this.normalizeAngle(this.state.heading);
  }

  /**
   * 获取陀螺仪偏置
   */
  getGyroBias() {
    return this.state.gyroBias;
  }

  /**
   * 获取滤波器状态信息
   */
  getFilterState() {
    return {
      heading: this.getHeading(),
      gyroBias: this.state.gyroBias,
      covariance: this.kf.P[0][0], // 航向角方差
      initialized: this.filterState.initialized,
      innovationMean: this.getInnovationMean()
    };
  }

  /**
   * 获取平均新息
   */
  getInnovationMean() {
    const history = this.filterState.innovationHistory;
    if (history.length === 0) return 0;
    
    return history.reduce((sum, val) => sum + val, 0) / history.length;
  }

  /**
   * 重置滤波器
   */
  reset() {
    this.state = {
      heading: 0,
      gyroBias: 0
    };
    
    this.kf.P = [
      [10, 0],
      [0, 1]
    ];
    
    this.filterState = {
      initialized: false,
      lastUpdate: 0,
      innovationHistory: []
    };
    
    this.sensorHistory = {
      gyroscope: [],
      compass: [],
      accelerometer: []
    };
    
    console.log('航向角估计器已重置');
  }

  /**
   * 设置配置参数
   */
  setConfig(config) {
    this.config = { ...this.config, ...config };
  }

  /**
   * 校准罗盘 - 8字校准法
   */
  async calibrateCompass(duration = 30000) {
    console.log('开始罗盘校准，请按8字形旋转设备...');
    
    const calibrationData = [];
    const startTime = Date.now();
    
    return new Promise((resolve) => {
      const collectData = () => {
        if (Date.now() - startTime >= duration) {
          // 校准完成，计算偏移量
          const avgHeading = calibrationData.reduce((sum, h) => sum + h, 0) / calibrationData.length;
          const compassOffset = -avgHeading; // 计算偏移量
          
          console.log('罗盘校准完成，偏移量:', compassOffset);
          resolve({ compassOffset, samples: calibrationData.length });
        } else {
          // 继续收集数据
          setTimeout(collectData, 100);
        }
      };
      
      // 模拟数据收集
      collectData();
    });
  }

  /**
   * 方向八分法 - 将连续角度转换为8个基本方向
   */
  getDirectionFromHeading(heading) {
    const normalizedHeading = this.normalizeAngle(heading);
    const adjustedHeading = (normalizedHeading + 360) % 360;
    
    const directions = [
      { name: '北', angle: 0, range: [337.5, 360, 0, 22.5] },
      { name: '东北', angle: 45, range: [22.5, 67.5] },
      { name: '东', angle: 90, range: [67.5, 112.5] },
      { name: '东南', angle: 135, range: [112.5, 157.5] },
      { name: '南', angle: 180, range: [157.5, 202.5] },
      { name: '西南', angle: 225, range: [202.5, 247.5] },
      { name: '西', angle: 270, range: [247.5, 292.5] },
      { name: '西北', angle: 315, range: [292.5, 337.5] }
    ];
    
    for (const dir of directions) {
      if (dir.range.length === 2) {
        if (adjustedHeading >= dir.range[0] && adjustedHeading < dir.range[1]) {
          return { name: dir.name, angle: dir.angle };
        }
      } else {
        // 处理跨越0度的情况（北方向）
        if (adjustedHeading >= dir.range[0] || adjustedHeading < dir.range[3]) {
          return { name: dir.name, angle: dir.angle };
        }
      }
    }
    
    return { name: '北', angle: 0 };
  }

  /**
   * 设置过程噪声协方差
   */
  setProcessNoise(headingNoise, biasNoise) {
    this.kf.Q = [
      [headingNoise, 0],
      [0, biasNoise]
    ];
  }

  /**
   * 设置观测噪声协方差
   */
  setMeasurementNoise(compassNoise) {
    this.kf.R = compassNoise;
  }
}

export default HeadingEstimator;