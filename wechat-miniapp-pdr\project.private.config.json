{"libVersion": "3.9.2", "projectname": "IndoorPDRMiniApp", "condition": {}, "setting": {"urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false, "useIsolateContext": true}}