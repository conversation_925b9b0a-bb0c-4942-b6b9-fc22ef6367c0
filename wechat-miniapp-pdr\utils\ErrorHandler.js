/**
 * 全局错误处理器
 * 用于处理微信小程序中的各种错误，特别是文件系统访问错误
 */

class ErrorHandler {
  constructor() {
    this.errorLog = [];
    this.maxLogSize = 50;
    this.isInitialized = false;
    
    // 错误类型统计
    this.errorStats = {
      fileSystemErrors: 0,
      networkErrors: 0,
      sensorErrors: 0,
      unknownErrors: 0
    };
  }

  /**
   * 初始化错误处理器
   */
  init() {
    if (this.isInitialized) return;
    
    // 设置全局错误处理
    this.setupGlobalErrorHandlers();
    
    // 重写console方法以捕获错误
    this.setupConsoleInterceptor();
    
    this.isInitialized = true;
    console.log('错误处理器已初始化');
  }

  /**
   * 设置全局错误处理器
   */
  setupGlobalErrorHandlers() {
    // 微信小程序错误处理
    if (typeof wx !== 'undefined') {
      // 监听小程序错误
      wx.onError && wx.onError((error) => {
        this.handleError('miniprogram', error);
      });
      
      // 监听未处理的Promise拒绝
      wx.onUnhandledRejection && wx.onUnhandledRejection((res) => {
        this.handleError('promise', res.reason);
      });
    }

    // 浏览器环境错误处理
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.handleError('javascript', event.error);
      });
      
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError('promise', event.reason);
      });
    }
  }

  /**
   * 设置控制台拦截器
   */
  setupConsoleInterceptor() {
    const originalError = console.error;
    const self = this;
    
    console.error = function(...args) {
      // 检查是否是文件系统错误
      const errorMessage = args.join(' ');
      if (self.isFileSystemError(errorMessage)) {
        self.handleFileSystemError(errorMessage);
        return; // 不输出到控制台，避免干扰
      }
      
      // 其他错误正常输出
      originalError.apply(console, args);
      self.handleError('console', errorMessage);
    };
  }

  /**
   * 检查是否是文件系统错误
   */
  isFileSystemError(message) {
    const fileSystemKeywords = [
      'wxfile://',
      'miniprogramLog',
      'no such file or directory',
      'access denied',
      'permission denied',
      'ENOENT',
      'EACCES'
    ];
    
    return fileSystemKeywords.some(keyword => 
      message.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * 处理文件系统错误
   */
  handleFileSystemError(message) {
    this.errorStats.fileSystemErrors++;
    
    // 记录错误但不显示给用户
    this.logError('filesystem', message, {
      timestamp: Date.now(),
      handled: true,
      severity: 'low'
    });
    
    // 如果是日志目录访问错误，尝试禁用相关功能
    if (message.includes('miniprogramLog')) {
      this.disableLoggingFeatures();
    }
  }

  /**
   * 禁用日志功能
   */
  disableLoggingFeatures() {
    // 重写可能导致文件访问的方法
    if (typeof wx !== 'undefined') {
      // 禁用文件系统相关API的错误输出
      const originalGetFileSystemManager = wx.getFileSystemManager;
      if (originalGetFileSystemManager) {
        wx.getFileSystemManager = function() {
          try {
            return originalGetFileSystemManager.call(wx);
          } catch (error) {
            // 静默处理文件系统管理器错误
            return {
              access: () => {},
              readFile: () => {},
              writeFile: () => {},
              mkdir: () => {},
              readdir: () => {}
            };
          }
        };
      }
    }
  }

  /**
   * 通用错误处理
   */
  handleError(type, error) {
    const errorInfo = {
      type: type,
      message: this.extractErrorMessage(error),
      timestamp: Date.now(),
      stack: error && error.stack ? error.stack : null
    };

    // 分类统计
    this.categorizeError(errorInfo);
    
    // 记录错误
    this.logError(type, errorInfo.message, errorInfo);
    
    // 根据错误类型决定是否需要特殊处理
    this.processError(errorInfo);
  }

  /**
   * 提取错误信息
   */
  extractErrorMessage(error) {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error && error.message) {
      return error.message;
    }
    
    if (error && error.errMsg) {
      return error.errMsg;
    }
    
    return String(error);
  }

  /**
   * 错误分类
   */
  categorizeError(errorInfo) {
    const message = errorInfo.message.toLowerCase();
    
    if (this.isFileSystemError(message)) {
      this.errorStats.fileSystemErrors++;
    } else if (message.includes('network') || message.includes('request')) {
      this.errorStats.networkErrors++;
    } else if (message.includes('sensor') || message.includes('accelerometer') || 
               message.includes('gyroscope') || message.includes('compass')) {
      this.errorStats.sensorErrors++;
    } else {
      this.errorStats.unknownErrors++;
    }
  }

  /**
   * 处理错误
   */
  processError(errorInfo) {
    // 文件系统错误 - 静默处理
    if (this.isFileSystemError(errorInfo.message)) {
      return;
    }
    
    // 传感器错误 - 记录但不中断
    if (errorInfo.message.toLowerCase().includes('sensor')) {
      console.warn('传感器错误:', errorInfo.message);
      return;
    }
    
    // 其他严重错误 - 正常处理
    if (errorInfo.type !== 'console') {
      console.warn('捕获到错误:', errorInfo.message);
    }
  }

  /**
   * 记录错误
   */
  logError(type, message, details = {}) {
    const logEntry = {
      type,
      message,
      details,
      timestamp: Date.now()
    };
    
    this.errorLog.push(logEntry);
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift();
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    return {
      ...this.errorStats,
      totalErrors: Object.values(this.errorStats).reduce((sum, count) => sum + count, 0),
      recentErrors: this.errorLog.slice(-10)
    };
  }

  /**
   * 获取错误日志
   */
  getErrorLog() {
    return [...this.errorLog];
  }

  /**
   * 清除错误日志
   */
  clearErrorLog() {
    this.errorLog = [];
    this.errorStats = {
      fileSystemErrors: 0,
      networkErrors: 0,
      sensorErrors: 0,
      unknownErrors: 0
    };
  }

  /**
   * 检查系统健康状态
   */
  getHealthStatus() {
    const stats = this.getErrorStats();
    const recentFileSystemErrors = this.errorLog
      .filter(log => Date.now() - log.timestamp < 60000) // 最近1分钟
      .filter(log => this.isFileSystemError(log.message))
      .length;
    
    return {
      overall: stats.totalErrors < 10 ? 'healthy' : 'warning',
      fileSystemHealth: recentFileSystemErrors < 5 ? 'healthy' : 'warning',
      sensorHealth: stats.sensorErrors < 3 ? 'healthy' : 'warning',
      networkHealth: stats.networkErrors < 5 ? 'healthy' : 'warning'
    };
  }

  /**
   * 安全执行函数
   */
  safeExecute(fn, fallback = null) {
    try {
      return fn();
    } catch (error) {
      this.handleError('safe_execute', error);
      return fallback;
    }
  }

  /**
   * 安全的异步执行
   */
  async safeExecuteAsync(fn, fallback = null) {
    try {
      return await fn();
    } catch (error) {
      this.handleError('safe_execute_async', error);
      return fallback;
    }
  }
}

// 创建全局实例
const globalErrorHandler = new ErrorHandler();

export default ErrorHandler;
export { globalErrorHandler };
