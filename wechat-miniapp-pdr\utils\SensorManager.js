/**
 * 传感器数据管理器
 * 负责采集和预处理传感器数据
 */

class SensorManager {
  constructor() {
    this.isCollecting = false;
    this.sampleRate = 50; // Hz
    this.dataBuffer = {
      accelerometer: [],
      gyroscope: [],
      compass: []
    };
    this.callbacks = {
      onAccelerometerData: null,
      onGyroscopeData: null,
      onCompassData: null,
      onError: null
    };
    
    // 滑动窗口配置
    this.windowSize = 50; // 1秒窗口
    this.slidingWindow = [];
    
    // 传感器偏置
    this.accelerometerBias = [0, 0, 0];
    this.gyroscopeBias = [0, 0, 0];
    
    this.lastTimestamp = 0;
  }

  /**
   * 启动传感器数据采集
   * @param {Object} config 配置参数
   */
  startCollection(config = {}) {
    if (this.isCollecting) {
      console.warn('传感器已在采集中');
      return;
    }

    this.sampleRate = config.sampleRate || this.sampleRate;
    this.windowSize = config.windowSize || this.windowSize;
    
    this.isCollecting = true;
    console.log('启动传感器数据采集, 采样率:', this.sampleRate + 'Hz');

    // 启动加速计
    this._startAccelerometer();
    
    // 启动陀螺仪
    this._startGyroscope();
    
    // 启动罗盘
    this._startCompass();
  }

  /**
   * 停止传感器数据采集
   */
  stopCollection() {
    if (!this.isCollecting) {
      return;
    }

    this.isCollecting = false;
    console.log('停止传感器数据采集');

    wx.stopAccelerometer();
    wx.stopGyroscope();
    wx.stopCompass();
    
    // 清空缓冲区
    this.clearBuffer();
  }

  /**
   * 启动加速计
   */
  _startAccelerometer() {
    wx.startAccelerometer({
      interval: 'fast', // 约20ms间隔
      success: () => {
        console.log('加速计启动成功');
        
        // 监听加速计数据
        wx.onAccelerometerChange((res) => {
          if (!this.isCollecting) return;
          
          const timestamp = Date.now();
          const data = {
            x: res.x - this.accelerometerBias[0],
            y: res.y - this.accelerometerBias[1], 
            z: res.z - this.accelerometerBias[2],
            timestamp: timestamp
          };

          // 计算合加速度和去重力加速度
          data.magnitude = Math.sqrt(data.x * data.x + data.y * data.y + data.z * data.z);
          data.linearAcceleration = data.magnitude - 9.8; // 去除重力加速度

          this.dataBuffer.accelerometer.push(data);
          this._maintainBufferSize('accelerometer');
          
          // 更新滑动窗口
          this._updateSlidingWindow(data, 'accelerometer');
          
          // 触发回调
          if (this.callbacks.onAccelerometerData) {
            this.callbacks.onAccelerometerData(data);
          }
        });
      },
      fail: (err) => {
        console.error('加速计启动失败:', err);
        if (this.callbacks.onError) {
          this.callbacks.onError('accelerometer', err);
        }
      }
    });
  }

  /**
   * 启动陀螺仪
   */
  _startGyroscope() {
    wx.startGyroscope({
      interval: 'fast',
      success: () => {
        console.log('陀螺仪启动成功');
        
        // 监听陀螺仪数据
        wx.onGyroscopeChange((res) => {
          if (!this.isCollecting) return;
          
          const timestamp = Date.now();
          const data = {
            x: res.x - this.gyroscopeBias[0],
            y: res.y - this.gyroscopeBias[1],
            z: res.z - this.gyroscopeBias[2],
            timestamp: timestamp
          };

          // 计算角速度幅值
          data.magnitude = Math.sqrt(data.x * data.x + data.y * data.y + data.z * data.z);

          this.dataBuffer.gyroscope.push(data);
          this._maintainBufferSize('gyroscope');
          
          // 更新滑动窗口
          this._updateSlidingWindow(data, 'gyroscope');
          
          // 触发回调
          if (this.callbacks.onGyroscopeData) {
            this.callbacks.onGyroscopeData(data);
          }
        });
      },
      fail: (err) => {
        console.error('陀螺仪启动失败:', err);
        if (this.callbacks.onError) {
          this.callbacks.onError('gyroscope', err);
        }
      }
    });
  }

  /**
   * 启动罗盘
   */
  _startCompass() {
    wx.startCompass({
      success: () => {
        console.log('罗盘启动成功');
        
        // 监听罗盘数据
        wx.onCompassChange((res) => {
          if (!this.isCollecting) return;
          
          const timestamp = Date.now();
          const data = {
            direction: res.direction,
            accuracy: res.accuracy,
            timestamp: timestamp
          };

          this.dataBuffer.compass.push(data);
          this._maintainBufferSize('compass');
          
          // 触发回调  
          if (this.callbacks.onCompassData) {
            this.callbacks.onCompassData(data);
          }
        });
      },
      fail: (err) => {
        console.error('罗盘启动失败:', err);
        if (this.callbacks.onError) {
          this.callbacks.onError('compass', err);
        }
      }
    });
  }

  /**
   * 更新滑动窗口
   */
  _updateSlidingWindow(data, type) {
    const windowData = {
      type: type,
      data: data,
      timestamp: data.timestamp
    };

    this.slidingWindow.push(windowData);
    
    // 维持窗口大小
    if (this.slidingWindow.length > this.windowSize) {
      this.slidingWindow.shift();
    }
  }

  /**
   * 维持缓冲区大小
   */
  _maintainBufferSize(sensorType) {
    const maxSize = this.windowSize * 2;
    if (this.dataBuffer[sensorType].length > maxSize) {
      this.dataBuffer[sensorType].splice(0, this.dataBuffer[sensorType].length - maxSize);
    }
  }

  /**
   * 获取最近的传感器数据
   */
  getRecentData(sensorType, count = 10) {
    const buffer = this.dataBuffer[sensorType];
    return buffer.slice(-count);
  }

  /**
   * 获取滑动窗口数据
   */
  getSlidingWindowData(sensorType = null) {
    if (sensorType) {
      return this.slidingWindow.filter(item => item.type === sensorType);
    }
    return this.slidingWindow;
  }

  /**
   * 计算方差
   */
  calculateVariance(data) {
    if (!data || data.length < 2) return 0;
    
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    
    return variance;
  }

  /**
   * 计算加速度方差（用于步态检测）
   */
  getAccelerometerVariance(windowSize = 20) {
    const recentData = this.getRecentData('accelerometer', windowSize);
    if (recentData.length < windowSize) return 0;
    
    const magnitudes = recentData.map(d => d.magnitude);
    return this.calculateVariance(magnitudes);
  }

  /**
   * 检测转向（基于陀螺仪数据）
   */
  detectTurn(threshold = 1.0) {
    const recentGyroData = this.getRecentData('gyroscope', 10);
    if (recentGyroData.length < 5) return false;
    
    const zValues = recentGyroData.map(d => d.z);
    const maxZ = Math.max(...zValues);
    const minZ = Math.min(...zValues);
    
    return Math.abs(maxZ) > threshold || Math.abs(minZ) > threshold;
  }

  /**
   * 设置传感器偏置（校准用）
   */
  setAccelerometerBias(bias) {
    this.accelerometerBias = [...bias];
    console.log('设置加速计偏置:', bias);
  }

  setGyroscopeBias(bias) {
    this.gyroscopeBias = [...bias];
    console.log('设置陀螺仪偏置:', bias);
  }

  /**
   * 校准传感器（静止状态下计算偏置）
   */
  async calibrateSensors(duration = 3000) {
    console.log('开始传感器校准，请保持设备静止...');
    
    const calibrationData = {
      accelerometer: [],
      gyroscope: []
    };

    // 临时回调收集校准数据
    const originalCallbacks = { ...this.callbacks };
    
    this.callbacks.onAccelerometerData = (data) => {
      calibrationData.accelerometer.push(data);
    };
    
    this.callbacks.onGyroscopeData = (data) => {
      calibrationData.gyroscope.push(data);
    };

    // 收集数据
    await new Promise(resolve => setTimeout(resolve, duration));

    // 计算偏置
    if (calibrationData.accelerometer.length > 0) {
      const accBias = [0, 0, 0];
      calibrationData.accelerometer.forEach(data => {
        accBias[0] += data.x;
        accBias[1] += data.y; 
        accBias[2] += data.z - 9.8; // 减去重力加速度
      });
      
      const accCount = calibrationData.accelerometer.length;
      this.accelerometerBias = [
        accBias[0] / accCount,
        accBias[1] / accCount, 
        accBias[2] / accCount
      ];
    }

    if (calibrationData.gyroscope.length > 0) {
      const gyroBias = [0, 0, 0];
      calibrationData.gyroscope.forEach(data => {
        gyroBias[0] += data.x;
        gyroBias[1] += data.y;
        gyroBias[2] += data.z;
      });
      
      const gyroCount = calibrationData.gyroscope.length;
      this.gyroscopeBias = [
        gyroBias[0] / gyroCount,
        gyroBias[1] / gyroCount,
        gyroBias[2] / gyroCount
      ];
    }

    // 恢复原始回调
    this.callbacks = originalCallbacks;
    
    console.log('传感器校准完成');
    console.log('加速计偏置:', this.accelerometerBias);
    console.log('陀螺仪偏置:', this.gyroscopeBias);
    
    return {
      accelerometerBias: this.accelerometerBias,
      gyroscopeBias: this.gyroscopeBias
    };
  }

  /**
   * 设置回调函数
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 清空数据缓冲区
   */
  clearBuffer() {
    this.dataBuffer = {
      accelerometer: [],
      gyroscope: [],
      compass: []
    };
    this.slidingWindow = [];
  }
}

export default SensorManager;