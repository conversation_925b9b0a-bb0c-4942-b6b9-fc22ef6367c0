/**
 * API废弃警告修复验证测试
 * 验证新API的使用和Canvas 2D兼容性
 */

// 模拟微信小程序环境
const mockWx = {
  // 新的API
  getWindowInfo: (options) => {
    console.log('✅ 使用新API: wx.getWindowInfo');
    setTimeout(() => {
      if (options.success) {
        options.success({
          windowWidth: 375,
          windowHeight: 667,
          safeArea: { top: 44, bottom: 34 }
        });
      }
    }, 50);
  },
  
  getDeviceInfo: (options) => {
    console.log('✅ 使用新API: wx.getDeviceInfo');
    setTimeout(() => {
      if (options.success) {
        options.success({
          pixelRatio: 2,
          platform: 'ios',
          system: 'iOS 15.0'
        });
      }
    }, 50);
  },
  
  getAppBaseInfo: () => {
    console.log('✅ 使用新API: wx.getAppBaseInfo');
    return {
      benchmarkLevel: 1,
      version: '8.0.5'
    };
  },
  
  // 旧的API（用于降级）
  getSystemInfo: (options) => {
    console.log('⚠️ 降级使用: wx.getSystemInfo');
    setTimeout(() => {
      if (options.success) {
        options.success({
          windowWidth: 375,
          windowHeight: 667,
          pixelRatio: 2,
          platform: 'ios',
          system: 'iOS 15.0',
          benchmarkLevel: 1
        });
      }
    }, 50);
  },
  
  getSystemInfoSync: () => {
    console.log('⚠️ 降级使用: wx.getSystemInfoSync');
    return {
      windowWidth: 375,
      windowHeight: 667,
      pixelRatio: 2,
      platform: 'ios',
      system: 'iOS 15.0',
      benchmarkLevel: 1
    };
  },
  
  // Canvas相关API
  createSelectorQuery: () => ({
    in: () => ({
      select: () => ({
        fields: () => ({
          exec: (callback) => {
            console.log('✅ 尝试Canvas 2D API');
            // 模拟Canvas 2D支持
            setTimeout(() => {
              callback([{
                node: {
                  getContext: (type) => {
                    if (type === '2d') {
                      console.log('✅ Canvas 2D API 可用');
                      return {
                        // Canvas 2D API方法
                        fillStyle: '#000',
                        strokeStyle: '#000',
                        lineWidth: 1,
                        clearRect: () => {},
                        fillRect: () => {},
                        beginPath: () => {},
                        moveTo: () => {},
                        lineTo: () => {},
                        arc: () => {},
                        fill: () => {},
                        stroke: () => {},
                        scale: () => {}
                      };
                    }
                    return null;
                  },
                  width: 0,
                  height: 0
                },
                width: 375,
                height: 200
              }]);
            }, 100);
          })
        })
      })
    })
  }),
  
  createCanvasContext: (id) => {
    console.log('⚠️ 降级使用传统Canvas API');
    return {
      // 传统Canvas API方法
      setFillStyle: () => {},
      setStrokeStyle: () => {},
      setLineWidth: () => {},
      clearRect: () => {},
      fillRect: () => {},
      beginPath: () => {},
      moveTo: () => {},
      lineTo: () => {},
      arc: () => {},
      fill: () => {},
      stroke: () => {},
      scale: () => {},
      draw: () => {}
    };
  }
};

global.wx = mockWx;

// 导入要测试的模块
const CanvasCompat = require('../utils/CanvasCompat.js');

/**
 * 测试新的系统信息API
 */
async function testNewSystemInfoAPI() {
  console.log('\n=== 测试新的系统信息API ===');
  
  try {
    // 测试getWindowInfo
    const windowInfo = await new Promise((resolve, reject) => {
      if (wx.getWindowInfo) {
        wx.getWindowInfo({
          success: resolve,
          fail: reject
        });
      } else {
        reject(new Error('getWindowInfo不可用'));
      }
    });
    
    console.log('窗口信息:', windowInfo);
    
    // 测试getDeviceInfo
    const deviceInfo = await new Promise((resolve, reject) => {
      if (wx.getDeviceInfo) {
        wx.getDeviceInfo({
          success: resolve,
          fail: reject
        });
      } else {
        reject(new Error('getDeviceInfo不可用'));
      }
    });
    
    console.log('设备信息:', deviceInfo);
    
    // 测试getAppBaseInfo
    if (wx.getAppBaseInfo) {
      const appInfo = wx.getAppBaseInfo();
      console.log('应用信息:', appInfo);
    }
    
    return {
      windowInfo,
      deviceInfo,
      success: true
    };
    
  } catch (error) {
    console.error('新API测试失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试Canvas 2D兼容性
 */
async function testCanvas2DCompatibility() {
  console.log('\n=== 测试Canvas 2D兼容性 ===');
  
  try {
    // 模拟Canvas 2D初始化
    const query = wx.createSelectorQuery();
    const result = await new Promise((resolve) => {
      query.select('#testCanvas')
        .fields({ node: true, size: true })
        .exec(resolve);
    });
    
    let canvasContext = null;
    let isCanvas2D = false;
    
    if (result[0] && result[0].node) {
      // Canvas 2D API
      const canvas = result[0].node;
      canvasContext = canvas.getContext('2d');
      isCanvas2D = true;
      console.log('✅ Canvas 2D API 初始化成功');
    } else {
      // 降级到传统API
      canvasContext = wx.createCanvasContext('testCanvas');
      isCanvas2D = false;
      console.log('⚠️ 降级到传统Canvas API');
    }
    
    // 测试兼容性工具
    const compat = new CanvasCompat(isCanvas2D);
    
    // 测试绘制操作
    compat.setFillStyle(canvasContext, '#ff0000');
    compat.setStrokeStyle(canvasContext, '#00ff00');
    compat.setLineWidth(canvasContext, 2);
    
    compat.beginPath(canvasContext);
    compat.arc(canvasContext, 50, 50, 20, 0, 2 * Math.PI);
    compat.fill(canvasContext);
    compat.stroke(canvasContext);
    
    compat.draw(canvasContext);
    
    console.log('✅ Canvas兼容性测试通过');
    
    return {
      isCanvas2D,
      success: true
    };
    
  } catch (error) {
    console.error('Canvas兼容性测试失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试API降级机制
 */
async function testAPIFallback() {
  console.log('\n=== 测试API降级机制 ===');
  
  // 临时禁用新API来测试降级
  const originalGetWindowInfo = wx.getWindowInfo;
  const originalGetDeviceInfo = wx.getDeviceInfo;
  
  delete wx.getWindowInfo;
  delete wx.getDeviceInfo;
  
  try {
    // 测试降级到getSystemInfo
    const systemInfo = await new Promise((resolve, reject) => {
      wx.getSystemInfo({
        success: resolve,
        fail: reject
      });
    });
    
    console.log('✅ 降级机制工作正常，获取到系统信息:', systemInfo);
    
    return { success: true, systemInfo };
    
  } catch (error) {
    console.error('降级机制测试失败:', error);
    return { success: false, error: error.message };
  } finally {
    // 恢复API
    wx.getWindowInfo = originalGetWindowInfo;
    wx.getDeviceInfo = originalGetDeviceInfo;
  }
}

/**
 * 测试Canvas兼容性工具类
 */
function testCanvasCompatClass() {
  console.log('\n=== 测试Canvas兼容性工具类 ===');
  
  // 测试Canvas 2D模式
  const compat2D = new CanvasCompat(true);
  const mockCtx2D = {
    fillStyle: '',
    strokeStyle: '',
    lineWidth: 1
  };
  
  compat2D.setFillStyle(mockCtx2D, '#ff0000');
  compat2D.setStrokeStyle(mockCtx2D, '#00ff00');
  compat2D.setLineWidth(mockCtx2D, 3);
  
  console.log('Canvas 2D模式测试:', {
    fillStyle: mockCtx2D.fillStyle,
    strokeStyle: mockCtx2D.strokeStyle,
    lineWidth: mockCtx2D.lineWidth
  });
  
  // 测试传统模式
  const compatLegacy = new CanvasCompat(false);
  const mockCtxLegacy = {
    setFillStyle: (color) => { mockCtxLegacy._fillStyle = color; },
    setStrokeStyle: (color) => { mockCtxLegacy._strokeStyle = color; },
    setLineWidth: (width) => { mockCtxLegacy._lineWidth = width; },
    _fillStyle: '',
    _strokeStyle: '',
    _lineWidth: 1
  };
  
  compatLegacy.setFillStyle(mockCtxLegacy, '#ff0000');
  compatLegacy.setStrokeStyle(mockCtxLegacy, '#00ff00');
  compatLegacy.setLineWidth(mockCtxLegacy, 3);
  
  console.log('传统Canvas模式测试:', {
    fillStyle: mockCtxLegacy._fillStyle,
    strokeStyle: mockCtxLegacy._strokeStyle,
    lineWidth: mockCtxLegacy._lineWidth
  });
  
  return {
    canvas2D: mockCtx2D.fillStyle === '#ff0000',
    legacy: mockCtxLegacy._fillStyle === '#ff0000',
    success: true
  };
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始API废弃警告修复验证测试...\n');
  
  const results = {
    newAPI: null,
    canvas2D: null,
    fallback: null,
    compatClass: null
  };
  
  try {
    // 测试新API
    results.newAPI = await testNewSystemInfoAPI();
    
    // 测试Canvas 2D
    results.canvas2D = await testCanvas2DCompatibility();
    
    // 测试降级机制
    results.fallback = await testAPIFallback();
    
    // 测试兼容性工具类
    results.compatClass = testCanvasCompatClass();
    
    // 输出测试总结
    console.log('\n=== 测试总结 ===');
    console.log('新API测试:', results.newAPI.success ? '✅ 通过' : '❌ 失败');
    console.log('Canvas 2D测试:', results.canvas2D.success ? '✅ 通过' : '❌ 失败');
    console.log('降级机制测试:', results.fallback.success ? '✅ 通过' : '❌ 失败');
    console.log('兼容性工具测试:', results.compatClass.success ? '✅ 通过' : '❌ 失败');
    
    const allPassed = Object.values(results).every(result => result.success);
    console.log(`\n总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 存在测试失败'}`);
    
    if (allPassed) {
      console.log('\n🎉 API废弃警告修复验证成功！');
      console.log('✅ 新API正常工作');
      console.log('✅ Canvas 2D兼容性良好');
      console.log('✅ 降级机制可靠');
      console.log('✅ 兼容性工具完善');
    }
    
    return allPassed;
    
  } catch (error) {
    console.error('❌ 测试运行失败:', error);
    return false;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests()
    .then((success) => {
      if (success) {
        console.log('\n✨ 所有API废弃警告修复验证通过！');
        process.exit(0);
      } else {
        console.log('\n💥 API废弃警告修复验证失败！');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  testNewSystemInfoAPI,
  testCanvas2DCompatibility,
  testAPIFallback,
  testCanvasCompatClass,
  runAllTests
};
