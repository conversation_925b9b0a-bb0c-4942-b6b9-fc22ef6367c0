<!--传感器校准页面-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <text class="title">传感器校准</text>
    <text class="subtitle">提高定位精度的关键步骤</text>
  </view>

  <!-- 校准状态卡片 -->
  <view class="status-card">
    <view class="status-header">
      <text class="status-title">校准状态</text>
      <view class="status-indicator {{calibrationStatus}}">
        <text class="indicator-text">{{statusText}}</text>
      </view>
    </view>
    
    <view class="calibration-progress" wx:if="{{isCalibrating}}">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progress}}%"></view>
      </view>
      <text class="progress-text">{{progress}}% - {{progressText}}</text>
    </view>
  </view>

  <!-- 校准项目 -->
  <view class="calibration-items">
    
    <!-- 加速计校准 -->
    <view class="calibration-item">
      <view class="item-header">
        <view class="item-icon acc-icon">📱</view>
        <view class="item-info">
          <text class="item-title">加速计校准</text>
          <text class="item-desc">消除设备静态偏置，提高步态检测精度</text>
        </view>
        <view class="item-status {{accelerometerStatus}}">
          <text class="status-icon">{{accelerometerStatusIcon}}</text>
        </view>
      </view>
      
      <view class="item-details" wx:if="{{showAccDetails}}">
        <view class="detail-row">
          <text class="detail-label">X轴偏置:</text>
          <text class="detail-value">{{accBias.x}} m/s²</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">Y轴偏置:</text>
          <text class="detail-value">{{accBias.y}} m/s²</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">Z轴偏置:</text>
          <text class="detail-value">{{accBias.z}} m/s²</text>
        </view>
      </view>
      
      <view class="item-actions">
        <button class="action-btn secondary" bindtap="toggleAccDetails">
          {{showAccDetails ? '隐藏详情' : '显示详情'}}
        </button>
        <button class="action-btn primary" bindtap="calibrateAccelerometer" disabled="{{isCalibrating}}">
          {{accelerometerStatus === 'success' ? '重新校准' : '开始校准'}}
        </button>
      </view>
    </view>

    <!-- 陀螺仪校准 -->
    <view class="calibration-item">
      <view class="item-header">
        <view class="item-icon gyro-icon">🎯</view>
        <view class="item-info">
          <text class="item-title">陀螺仪校准</text>
          <text class="item-desc">校正角速度偏差，改善航向估计</text>
        </view>
        <view class="item-status {{gyroscopeStatus}}">
          <text class="status-icon">{{gyroscopeStatusIcon}}</text>
        </view>
      </view>
      
      <view class="item-details" wx:if="{{showGyroDetails}}">
        <view class="detail-row">
          <text class="detail-label">X轴偏置:</text>
          <text class="detail-value">{{gyroBias.x}} rad/s</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">Y轴偏置:</text>
          <text class="detail-value">{{gyroBias.y}} rad/s</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">Z轴偏置:</text>
          <text class="detail-value">{{gyroBias.z}} rad/s</text>
        </view>
      </view>
      
      <view class="item-actions">
        <button class="action-btn secondary" bindtap="toggleGyroDetails">
          {{showGyroDetails ? '隐藏详情' : '显示详情'}}
        </button>
        <button class="action-btn primary" bindtap="calibrateGyroscope" disabled="{{isCalibrating}}">
          {{gyroscopeStatus === 'success' ? '重新校准' : '开始校准'}}
        </button>
      </view>
    </view>

    <!-- 罗盘校准 -->
    <view class="calibration-item">
      <view class="item-header">
        <view class="item-icon compass-icon">🧭</view>
        <view class="item-info">
          <text class="item-title">罗盘校准</text>
          <text class="item-desc">8字校准法消除磁场干扰</text>
        </view>
        <view class="item-status {{compassStatus}}">
          <text class="status-icon">{{compassStatusIcon}}</text>
        </view>
      </view>
      
      <view class="item-details" wx:if="{{showCompassDetails}}">
        <view class="detail-row">
          <text class="detail-label">磁偏角:</text>
          <text class="detail-value">{{magneticDeclination}}°</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">校准精度:</text>
          <text class="detail-value">{{compassAccuracy}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">样本数量:</text>
          <text class="detail-value">{{compassSamples}}</text>
        </view>
      </view>
      
      <view class="item-actions">
        <button class="action-btn secondary" bindtap="toggleCompassDetails">
          {{showCompassDetails ? '隐藏详情' : '显示详情'}}
        </button>
        <button class="action-btn primary" bindtap="calibrateCompass" disabled="{{isCalibrating}}">
          {{compassStatus === 'success' ? '重新校准' : '开始校准'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 校准指导 -->
  <view class="guidance-card" wx:if="{{showGuidance}}">
    <view class="guidance-header">
      <text class="guidance-title">校准指导</text>
      <view class="close-btn" bindtap="hideGuidance">✕</view>
    </view>
    
    <view class="guidance-content">
      <view class="guidance-step">
        <text class="step-number">1</text>
        <view class="step-content">
          <text class="step-title">加速计校准</text>
          <text class="step-desc">将设备平放在水平面上，保持静止3秒钟</text>
        </view>
      </view>
      
      <view class="guidance-step">
        <text class="step-number">2</text>
        <view class="step-content">
          <text class="step-title">陀螺仪校准</text>
          <text class="step-desc">设备保持完全静止，避免任何旋转或震动</text>
        </view>
      </view>
      
      <view class="guidance-step">
        <text class="step-number">3</text>
        <view class="step-content">
          <text class="step-title">罗盘校准</text>
          <text class="step-desc">按8字形缓慢旋转设备，覆盖所有方向</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 一键校准 -->
  <view class="quick-calibration">
    <button class="quick-btn" bindtap="quickCalibration" disabled="{{isCalibrating}}">
      <text class="btn-icon">⚡</text>
      <text class="btn-text">一键校准所有传感器</text>
    </button>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="action-button secondary" bindtap="resetCalibration" disabled="{{isCalibrating}}">
      重置校准数据
    </button>
    <button class="action-button primary" bindtap="saveCalibration" disabled="{{!hasValidCalibration || isCalibrating}}">
      保存并应用
    </button>
  </view>

  <!-- 校准动画 -->
  <view class="calibration-animation" wx:if="{{showAnimation}}">
    <view class="animation-content">
      <view class="device-icon {{animationType}}">📱</view>
      <text class="animation-text">{{animationText}}</text>
      <view class="animation-indicator">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
    </view>
  </view>

  <!-- 提示信息 -->
  <view class="toast {{showToast ? 'show' : ''}}" bindtap="hideToast">
    <text>{{toastMessage}}</text>
  </view>
</view>