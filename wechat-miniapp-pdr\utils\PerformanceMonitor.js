/**
 * 性能监控工具
 * 用于监控PDR系统的性能指标和启动时间
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      startupTime: 0,
      sensorInitTime: 0,
      firstDataTime: 0,
      avgProcessingTime: 0,
      frameRate: 0,
      memoryUsage: 0
    };
    
    this.timers = new Map();
    this.counters = new Map();
    this.history = [];
    
    this.isMonitoring = false;
    this.startTime = 0;
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    this.isMonitoring = true;
    this.startTime = Date.now();
    this.metrics = {
      startupTime: 0,
      sensorInitTime: 0,
      firstDataTime: 0,
      avgProcessingTime: 0,
      frameRate: 0,
      memoryUsage: 0
    };
    
    console.log('性能监控已开始');
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false;
    console.log('性能监控已停止');
    return this.getReport();
  }

  /**
   * 开始计时
   */
  startTimer(name) {
    if (!this.isMonitoring) return;
    
    this.timers.set(name, Date.now());
  }

  /**
   * 结束计时
   */
  endTimer(name) {
    if (!this.isMonitoring || !this.timers.has(name)) return 0;
    
    const startTime = this.timers.get(name);
    const duration = Date.now() - startTime;
    this.timers.delete(name);
    
    // 记录特殊指标
    switch (name) {
      case 'startup':
        this.metrics.startupTime = duration;
        break;
      case 'sensorInit':
        this.metrics.sensorInitTime = duration;
        break;
      case 'firstData':
        this.metrics.firstDataTime = duration;
        break;
    }
    
    return duration;
  }

  /**
   * 记录处理时间
   */
  recordProcessingTime(duration) {
    if (!this.isMonitoring) return;
    
    const count = this.counters.get('processingCount') || 0;
    const total = this.counters.get('processingTotal') || 0;
    
    this.counters.set('processingCount', count + 1);
    this.counters.set('processingTotal', total + duration);
    
    this.metrics.avgProcessingTime = (total + duration) / (count + 1);
  }

  /**
   * 记录帧率
   */
  recordFrame() {
    if (!this.isMonitoring) return;
    
    const now = Date.now();
    const lastFrame = this.counters.get('lastFrame') || now;
    const frameInterval = now - lastFrame;
    
    if (frameInterval > 0) {
      const fps = 1000 / frameInterval;
      const frameCount = this.counters.get('frameCount') || 0;
      const totalFps = this.counters.get('totalFps') || 0;
      
      this.counters.set('frameCount', frameCount + 1);
      this.counters.set('totalFps', totalFps + fps);
      this.counters.set('lastFrame', now);
      
      this.metrics.frameRate = (totalFps + fps) / (frameCount + 1);
    }
  }

  /**
   * 记录内存使用情况
   */
  recordMemoryUsage() {
    if (!this.isMonitoring) return;

    try {
      // 微信小程序中获取内存信息
      if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
        const memInfo = wx.getSystemInfoSync();
        if (memInfo.benchmarkLevel) {
          this.metrics.memoryUsage = memInfo.benchmarkLevel;
        }
      }
    } catch (error) {
      // 静默处理内存信息获取失败，避免影响主要功能
      // console.warn('无法获取内存信息:', error);
    }
  }

  /**
   * 记录事件
   */
  recordEvent(eventName, data = {}) {
    if (!this.isMonitoring) return;
    
    const event = {
      name: eventName,
      timestamp: Date.now(),
      relativeTime: Date.now() - this.startTime,
      data: data
    };
    
    this.history.push(event);
    
    // 限制历史记录数量
    if (this.history.length > 100) {
      this.history.shift();
    }
    
    console.log(`[性能监控] ${eventName}:`, data);
  }

  /**
   * 获取性能报告
   */
  getReport() {
    const report = {
      metrics: { ...this.metrics },
      counters: Object.fromEntries(this.counters),
      history: [...this.history],
      totalTime: Date.now() - this.startTime,
      timestamp: Date.now()
    };
    
    // 计算一些衍生指标
    report.derived = {
      isSlowStartup: this.metrics.startupTime > 3000,
      isSlowSensorInit: this.metrics.sensorInitTime > 2000,
      isSlowFirstData: this.metrics.firstDataTime > 1000,
      isLowFrameRate: this.metrics.frameRate < 10,
      isHighProcessingTime: this.metrics.avgProcessingTime > 50
    };
    
    return report;
  }

  /**
   * 获取实时指标
   */
  getRealTimeMetrics() {
    return {
      ...this.metrics,
      uptime: Date.now() - this.startTime,
      isMonitoring: this.isMonitoring
    };
  }

  /**
   * 诊断启动问题
   */
  diagnoseStartupIssues() {
    const report = this.getReport();
    const issues = [];
    
    if (report.derived.isSlowStartup) {
      issues.push({
        type: 'slow_startup',
        severity: 'high',
        message: `启动时间过长: ${report.metrics.startupTime}ms`,
        suggestion: '检查模块初始化顺序和传感器权限'
      });
    }
    
    if (report.derived.isSlowSensorInit) {
      issues.push({
        type: 'slow_sensor_init',
        severity: 'high',
        message: `传感器初始化时间过长: ${report.metrics.sensorInitTime}ms`,
        suggestion: '检查传感器权限和设备兼容性'
      });
    }
    
    if (report.derived.isSlowFirstData) {
      issues.push({
        type: 'slow_first_data',
        severity: 'medium',
        message: `首次数据获取时间过长: ${report.metrics.firstDataTime}ms`,
        suggestion: '检查传感器数据回调和处理逻辑'
      });
    }
    
    if (report.derived.isLowFrameRate) {
      issues.push({
        type: 'low_frame_rate',
        severity: 'medium',
        message: `帧率过低: ${report.metrics.frameRate.toFixed(1)}fps`,
        suggestion: '优化UI更新频率和画布渲染'
      });
    }
    
    if (report.derived.isHighProcessingTime) {
      issues.push({
        type: 'high_processing_time',
        severity: 'medium',
        message: `处理时间过长: ${report.metrics.avgProcessingTime.toFixed(1)}ms`,
        suggestion: '优化算法计算和数据处理逻辑'
      });
    }
    
    return {
      issues,
      report,
      summary: {
        totalIssues: issues.length,
        highSeverityIssues: issues.filter(i => i.severity === 'high').length,
        overallHealth: issues.length === 0 ? 'good' : 
                      issues.filter(i => i.severity === 'high').length > 0 ? 'poor' : 'fair'
      }
    };
  }

  /**
   * 输出性能报告到控制台
   */
  printReport() {
    const diagnosis = this.diagnoseStartupIssues();
    
    console.log('=== PDR性能监控报告 ===');
    console.log('启动时间:', diagnosis.report.metrics.startupTime + 'ms');
    console.log('传感器初始化时间:', diagnosis.report.metrics.sensorInitTime + 'ms');
    console.log('首次数据时间:', diagnosis.report.metrics.firstDataTime + 'ms');
    console.log('平均处理时间:', diagnosis.report.metrics.avgProcessingTime.toFixed(1) + 'ms');
    console.log('平均帧率:', diagnosis.report.metrics.frameRate.toFixed(1) + 'fps');
    console.log('总运行时间:', diagnosis.report.totalTime + 'ms');
    
    if (diagnosis.issues.length > 0) {
      console.log('\n=== 发现的问题 ===');
      diagnosis.issues.forEach((issue, index) => {
        console.log(`${index + 1}. [${issue.severity.toUpperCase()}] ${issue.message}`);
        console.log(`   建议: ${issue.suggestion}`);
      });
    } else {
      console.log('\n✅ 未发现性能问题');
    }
    
    console.log(`\n整体健康状况: ${diagnosis.summary.overallHealth.toUpperCase()}`);
  }
}

export default PerformanceMonitor;
