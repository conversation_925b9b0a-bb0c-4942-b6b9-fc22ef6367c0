代码基于android进行开发，使用android studio作为开发工具，使用版本为3.4，使用其他版本可能会出现版本兼容问题，需要自行调整。
本代码主要内容为PDR及MLA节点匹配，参考论文《MLA-MFL: A Smartphone Indoor Localization Method for Fusing Multi-source Sensors under Multiple Scene Conditions 》

The code is developed based on Android, utilizing Android Studio (version 3.4) as the development tool. Compatibility issues may arise when using other versions, which would require manual adjustments.

The implementation primarily focuses on PDR and MLA node matching, referencing the paper: MLA-MFL: A Smartphone Indoor Localization Method for Fusing Multi-source Sensors under Multiple Scene Conditions.