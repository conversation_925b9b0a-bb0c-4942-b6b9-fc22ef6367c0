// 首页 - 室内惯导定位主界面
import SensorManager from '../../utils/SensorManager.js';
import StepDetector from '../../utils/StepDetector.js';
import HeadingEstimator from '../../utils/HeadingEstimator.js';
import MLAMatcher from '../../utils/MLAMatcher.js';
import PerformanceMonitor from '../../utils/PerformanceMonitor.js';

const app = getApp();

Page({
  data: {
    // 定位状态
    trackingStatus: false,
    isLoading: false,
    loadingText: '初始化中...',
    
    // 位置信息
    position: { x: 0, y: 0, z: 0 },
    heading: 0,
    stepCount: 0,
    stepLength: 0.75,
    velocity: 0,
    currentFloor: 4,
    
    // 传感器数据
    accelerometer: { magnitude: 0 },
    gyroscope: { magnitude: 0 },
    compass: { direction: 0 },
    
    // MLA匹配信息
    lastMatch: null,
    
    // UI状态
    showDetails: false,
    showToast: false,
    toastMessage: '',
    
    // 画布参数
    canvasWidth: 0,
    canvasHeight: 0,
    scale: 10, // 10像素/米
    offsetX: 0,
    offsetY: 0,
    trackingPath: []
  },

  // 算法模块实例
  sensorManager: null,
  stepDetector: null,
  headingEstimator: null,
  mlaMatcher: null,
  canvasContext: null,
  performanceMonitor: null,
  
  // 定位状态
  lastUpdateTime: 0,
  lastStepTime: 0,

  // 性能优化
  lastUIUpdateTime: 0,
  lastCanvasUpdateTime: 0,
  uiUpdateThrottle: 100, // UI更新节流间隔(ms)
  canvasUpdateThrottle: 200, // 画布更新节流间隔(ms)
  firstDataReceived: false, // 是否已收到首次数据

  onLoad: function (options) {
    console.log('室内惯导定位页面加载');

    // 获取全局错误处理器
    const app = getApp();
    if (app.globalData.errorHandler) {
      this.errorHandler = app.globalData.errorHandler;
    }

    this.initializePage();
  },

  onShow: function () {
    this.setupCanvas();
  },

  onHide: function () {
    if (this.data.trackingStatus) {
      this.stopTracking();
    }
  },

  onUnload: function () {
    this.cleanup();
  },

  /**
   * 初始化页面 - 优化版本
   */
  async initializePage() {
    // 启动性能监控
    this.performanceMonitor = new PerformanceMonitor();
    this.performanceMonitor.startMonitoring();
    this.performanceMonitor.startTimer('startup');

    this.setData({
      isLoading: true,
      loadingText: '初始化算法模块...'
    });

    try {
      // 初始化算法模块
      this.performanceMonitor.startTimer('moduleInit');
      await this.initializeModules();
      this.performanceMonitor.endTimer('moduleInit');
      this.performanceMonitor.recordEvent('modules_initialized');

      // 获取系统信息
      await this.getSystemInfo();

      this.performanceMonitor.endTimer('startup');
      this.performanceMonitor.recordEvent('page_initialized');

      this.setData({ isLoading: false });
      this.showToast('初始化完成，可以开始定位');

      // 输出性能报告
      setTimeout(() => {
        this.performanceMonitor.printReport();
      }, 1000);
    } catch (error) {
      console.error('页面初始化失败:', error);
      this.performanceMonitor.recordEvent('initialization_failed', { error: error.message });
      this.setData({
        isLoading: false
      });
      this.showToast('初始化失败，请重试');
    }
  },

  /**
   * 初始化算法模块
   */
  async initializeModules() {
    // 初始化传感器管理器
    this.sensorManager = new SensorManager();
    this.sensorManager.setCallbacks({
      onAccelerometerData: this.onAccelerometerData.bind(this),
      onGyroscopeData: this.onGyroscopeData.bind(this),
      onCompassData: this.onCompassData.bind(this),
      onError: this.onSensorError.bind(this)
    });

    // 初始化步态检测器
    this.stepDetector = new StepDetector();
    this.stepDetector.setStepDetectedCallback(this.onStepDetected.bind(this));

    // 初始化航向角估计器
    this.headingEstimator = new HeadingEstimator();
    // 优化：启用快速启动模式
    this.headingEstimator.quickStart(0);

    // 初始化MLA匹配器
    this.mlaMatcher = new MLAMatcher();
    this.mlaMatcher.initializeMlaDatabase();
    this.mlaMatcher.setCallbacks({
      onNodeMatched: this.onMlaNodeMatched.bind(this),
      onPositionCorrected: this.onPositionCorrected.bind(this)
    });

    console.log('算法模块初始化完成');
  },

  /**
   * 获取系统信息
   */
  async getSystemInfo() {
    return new Promise((resolve) => {
      wx.getSystemInfo({
        success: (res) => {
          const pixelRatio = res.pixelRatio || 2;
          this.setData({
            canvasWidth: res.windowWidth * pixelRatio,
            canvasHeight: res.windowHeight * 0.4 * pixelRatio
          });
          resolve();
        },
        fail: () => {
          resolve();
        }
      });
    });
  },

  /**
   * 设置画布
   */
  setupCanvas() {
    this.canvasContext = wx.createCanvasContext('trackingCanvas', this);
    this.canvasContext.scale(2, 2); // 适配高DPI屏幕
    this.drawCanvas();
  },

  /**
   * 绘制画布
   */
  drawCanvas() {
    const ctx = this.canvasContext;
    if (!ctx) return;

    const width = this.data.canvasWidth / 2;
    const height = this.data.canvasHeight / 2;

    // 清空画布
    ctx.clearRect(0, 0, width, height);
    ctx.setFillStyle('#fafafa');
    ctx.fillRect(0, 0, width, height);

    // 绘制网格
    this.drawGrid(ctx, width, height);
    
    // 绘制MLA节点
    this.drawMlaNodes(ctx, width, height);
    
    // 绘制轨迹
    this.drawTrackingPath(ctx, width, height);
    
    // 绘制当前位置
    this.drawCurrentPosition(ctx, width, height);

    ctx.draw();
  },

  /**
   * 绘制网格
   */
  drawGrid(ctx, width, height) {
    const scale = this.data.scale;
    const offsetX = this.data.offsetX;
    const offsetY = this.data.offsetY;
    
    ctx.setStrokeStyle('#e0e0e0');
    ctx.setLineWidth(0.5);
    
    // 绘制垂直线
    for (let x = offsetX % (scale * 5); x < width; x += scale * 5) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
    
    // 绘制水平线
    for (let y = offsetY % (scale * 5); y < height; y += scale * 5) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }

    // 绘制坐标轴
    const centerX = width / 2 + offsetX;
    const centerY = height / 2 + offsetY;
    
    if (centerX >= 0 && centerX <= width) {
      ctx.setStrokeStyle('#4CAF50');
      ctx.setLineWidth(2);
      ctx.beginPath();
      ctx.moveTo(centerX, 0);
      ctx.lineTo(centerX, height);
      ctx.stroke();
    }
    
    if (centerY >= 0 && centerY <= height) {
      ctx.setStrokeStyle('#2196F3');
      ctx.setLineWidth(2);
      ctx.beginPath();
      ctx.moveTo(0, centerY);
      ctx.lineTo(width, centerY);
      ctx.stroke();
    }
  },

  /**
   * 绘制MLA节点
   */
  drawMlaNodes(ctx, width, height) {
    const mlaData = this.mlaMatcher.mlaDatabase;
    const currentFloor = this.data.currentFloor;
    
    // 节点类型颜色
    const nodeColors = {
      gyro: '#FF9800',
      pressure: '#9C27B0', 
      accelerometer: '#F44336',
      floor: '#795548'
    };
    
    Object.keys(mlaData).forEach(nodeType => {
      const nodes = mlaData[nodeType].filter(node => node.floor === currentFloor);
      
      nodes.forEach(node => {
        const screenPos = this.worldToScreen(node.x, node.z, width, height);
        
        ctx.setFillStyle(nodeColors[nodeType] || '#666');
        ctx.beginPath();
        ctx.arc(screenPos.x, screenPos.y, 6, 0, 2 * Math.PI);
        ctx.fill();
        
        // 绘制节点标签
        ctx.setFillStyle('#333');
        ctx.setFontSize(10);
        ctx.fillText(node.description || `${nodeType}-${node.id}`, 
          screenPos.x + 8, screenPos.y - 8);
      });
    });
  },

  /**
   * 绘制轨迹路径
   */
  drawTrackingPath(ctx, width, height) {
    const path = this.data.trackingPath;
    if (path.length < 2) return;

    ctx.setStrokeStyle('#2196F3');
    ctx.setLineWidth(2);
    ctx.beginPath();
    
    const startPos = this.worldToScreen(path[0].x, path[0].y, width, height);
    ctx.moveTo(startPos.x, startPos.y);
    
    for (let i = 1; i < path.length; i++) {
      const pos = this.worldToScreen(path[i].x, path[i].y, width, height);
      ctx.lineTo(pos.x, pos.y);
    }
    
    ctx.stroke();

    // 绘制轨迹点
    path.forEach(point => {
      const screenPos = this.worldToScreen(point.x, point.y, width, height);
      ctx.setFillStyle('#1976D2');
      ctx.beginPath();
      ctx.arc(screenPos.x, screenPos.y, 2, 0, 2 * Math.PI);
      ctx.fill();
    });
  },

  /**
   * 绘制当前位置
   */
  drawCurrentPosition(ctx, width, height) {
    const position = this.data.position;
    const screenPos = this.worldToScreen(position.x, position.y, width, height);
    
    // 绘制位置点
    ctx.setFillStyle('#4CAF50');
    ctx.beginPath();
    ctx.arc(screenPos.x, screenPos.y, 8, 0, 2 * Math.PI);
    ctx.fill();
    
    // 绘制方向指示
    const heading = this.data.heading * Math.PI / 180;
    const arrowLength = 20;
    const arrowX = screenPos.x + Math.cos(heading) * arrowLength;
    const arrowY = screenPos.y + Math.sin(heading) * arrowLength;
    
    ctx.setStrokeStyle('#4CAF50');
    ctx.setLineWidth(3);
    ctx.beginPath();
    ctx.moveTo(screenPos.x, screenPos.y);
    ctx.lineTo(arrowX, arrowY);
    ctx.stroke();
    
    // 绘制箭头
    const arrowSize = 8;
    ctx.beginPath();
    ctx.moveTo(arrowX, arrowY);
    ctx.lineTo(
      arrowX - arrowSize * Math.cos(heading - Math.PI / 6),
      arrowY - arrowSize * Math.sin(heading - Math.PI / 6)
    );
    ctx.moveTo(arrowX, arrowY);
    ctx.lineTo(
      arrowX - arrowSize * Math.cos(heading + Math.PI / 6),
      arrowY - arrowSize * Math.sin(heading + Math.PI / 6)
    );
    ctx.stroke();
  },

  /**
   * 世界坐标转屏幕坐标
   */
  worldToScreen(worldX, worldY, screenWidth, screenHeight) {
    const scale = this.data.scale;
    const offsetX = this.data.offsetX;
    const offsetY = this.data.offsetY;
    
    return {
      x: (worldX * scale) + (screenWidth / 2) + offsetX,
      y: (screenHeight / 2) - (worldY * scale) + offsetY
    };
  },

  /**
   * 切换定位状态
   */
  async toggleTracking() {
    if (this.data.trackingStatus) {
      this.stopTracking();
    } else {
      await this.startTracking();
    }
  },

  /**
   * 开始定位 - 优化版本
   */
  async startTracking() {
    // 开始监控启动性能
    if (this.performanceMonitor) {
      this.performanceMonitor.startTimer('sensorInit');
      this.performanceMonitor.startTimer('firstData');
    }

    this.setData({
      isLoading: true,
      loadingText: '启动传感器...'
    });

    try {
      // 请求位置权限
      await this.requestLocationPermission();

      // 重置状态（在传感器启动前重置，避免等待）
      this.resetTrackingData();

      this.setData({
        loadingText: '初始化传感器...'
      });

      // 优化：使用异步启动传感器，有超时保护
      await this.sensorManager.startCollection({
        sampleRate: app.globalData.config.sampleRate,
        windowSize: app.globalData.config.filterWindow
      });

      if (this.performanceMonitor) {
        this.performanceMonitor.endTimer('sensorInit');
        this.performanceMonitor.recordEvent('sensors_started');
      }

      this.setData({
        trackingStatus: true,
        isLoading: false
      });

      this.showToast('定位已开始');
      console.log('室内定位启动成功');
    } catch (error) {
      console.error('启动定位失败:', error);
      if (this.performanceMonitor) {
        this.performanceMonitor.recordEvent('tracking_start_failed', { error: error.message });
      }
      this.setData({ isLoading: false });
      this.showToast('启动失败: ' + error.message);
    }
  },

  /**
   * 停止定位
   */
  stopTracking() {
    this.sensorManager.stopCollection();
    
    this.setData({ trackingStatus: false });
    this.showToast('定位已停止');
    
    console.log('室内定位已停止');
  },

  /**
   * 重置定位数据
   */
  resetTrackingData() {
    // 重置算法模块
    this.stepDetector.reset();
    this.headingEstimator.reset();
    this.mlaMatcher.reset();
    
    // 重置UI数据
    this.setData({
      position: { x: 0, y: 0, z: 0 },
      heading: 0,
      stepCount: 0,
      stepLength: 0.75,
      velocity: 0,
      trackingPath: [],
      lastMatch: null
    });

    this.lastUpdateTime = 0;
    this.lastStepTime = 0;
    this.firstDataReceived = false;

    // 重绘画布
    this.drawCanvas();
  },

  /**
   * 传感器数据回调 - 优化版本
   */
  onAccelerometerData(data) {
    // 监控首次数据接收
    if (!this.firstDataReceived && this.performanceMonitor) {
      this.performanceMonitor.endTimer('firstData');
      this.performanceMonitor.recordEvent('first_data_received', { type: 'accelerometer' });
      this.firstDataReceived = true;
    }

    // 记录处理时间
    const startTime = Date.now();

    // 优化：节流UI更新
    const now = Date.now();
    if (now - this.lastUIUpdateTime > this.uiUpdateThrottle) {
      this.setData({
        accelerometer: {
          magnitude: data.magnitude.toFixed(2)
        }
      });
      this.lastUIUpdateTime = now;
    }

    // 步态检测
    const stepResult = this.stepDetector.processAccelerometerData(data);

    // 传递给MLA匹配器
    this.processMlaMatching({
      accelerometer: [data], // 简化处理，实际应该传递历史数据
      timestamp: data.timestamp
    });

    // 记录处理时间
    if (this.performanceMonitor) {
      this.performanceMonitor.recordProcessingTime(Date.now() - startTime);
    }
  },

  onGyroscopeData(data) {
    // 优化：节流UI更新
    const now = Date.now();
    if (now - this.lastUIUpdateTime > this.uiUpdateThrottle) {
      this.setData({
        gyroscope: {
          magnitude: data.magnitude.toFixed(2)
        }
      });
    }

    // 航向角估计
    const heading = this.headingEstimator.updateHeading({
      gyroZ: data.z
    }, data.timestamp);

    // 优化：只在航向角有明显变化时更新UI
    const currentHeading = parseFloat(this.data.heading) || 0;
    if (Math.abs(heading - currentHeading) > 1.0 || now - this.lastUIUpdateTime > this.uiUpdateThrottle) {
      this.setData({ heading: heading.toFixed(1) });
      this.lastUIUpdateTime = now;
    }

    // MLA匹配
    this.processMlaMatching({
      gyroscope: data,
      timestamp: data.timestamp
    });
  },

  onCompassData(data) {
    // 优化：节流UI更新
    const now = Date.now();
    if (now - this.lastUIUpdateTime > this.uiUpdateThrottle) {
      this.setData({
        compass: {
          direction: data.direction.toFixed(1)
        }
      });
    }

    // 航向角估计
    const heading = this.headingEstimator.updateHeading({
      compassHeading: data.direction
    }, data.timestamp);

    // 优化：只在航向角有明显变化时更新UI
    const currentHeading = parseFloat(this.data.heading) || 0;
    if (Math.abs(heading - currentHeading) > 1.0 || now - this.lastUIUpdateTime > this.uiUpdateThrottle) {
      this.setData({ heading: heading.toFixed(1) });
      this.lastUIUpdateTime = now;
    }
  },

  onSensorError(sensorType, error) {
    console.error(`${sensorType}传感器错误:`, error);
    this.showToast(`${sensorType}传感器异常`);
  },

  /**
   * 步态检测回调
   */
  onStepDetected(stepResult) {
    console.log('检测到步态:', stepResult);
    
    // 更新步数和步长
    this.setData({
      stepCount: stepResult.stepCount,
      stepLength: stepResult.stepLength.toFixed(2)
    });

    // 计算速度
    const currentTime = stepResult.timestamp;
    if (this.lastStepTime > 0) {
      const timeDiff = (currentTime - this.lastStepTime) / 1000; // 秒
      const velocity = stepResult.stepLength / timeDiff;
      this.setData({
        velocity: velocity.toFixed(2)
      });
    }
    this.lastStepTime = currentTime;

    // 更新位置
    this.updatePosition(stepResult);
  },

  /**
   * 更新位置
   */
  updatePosition(stepResult) {
    const currentPos = this.data.position;
    const heading = this.data.heading * Math.PI / 180; // 转弧度
    const stepLength = stepResult.stepLength;

    // PDR位置更新
    const newPosition = {
      x: currentPos.x + stepLength * Math.cos(heading),
      y: currentPos.y + stepLength * Math.sin(heading),
      z: currentPos.z
    };

    // 更新轨迹路径
    const newPath = [...this.data.trackingPath];
    newPath.push({ ...newPosition, timestamp: stepResult.timestamp });
    
    // 限制轨迹点数量
    if (newPath.length > 1000) {
      newPath.shift();
    }

    this.setData({
      position: {
        x: parseFloat(newPosition.x.toFixed(2)),
        y: parseFloat(newPosition.y.toFixed(2)),
        z: parseFloat(newPosition.z.toFixed(2))
      },
      trackingPath: newPath
    });

    // 优化：节流画布更新
    const now = Date.now();
    if (now - this.lastCanvasUpdateTime > this.canvasUpdateThrottle) {
      this.drawCanvas();
      this.lastCanvasUpdateTime = now;

      // 记录帧率
      if (this.performanceMonitor) {
        this.performanceMonitor.recordFrame();
      }
    }
  },

  /**
   * MLA匹配处理
   */
  processMlaMatching(sensorData) {
    const currentPosition = this.data.position;
    const matchResult = this.mlaMatcher.matchNodes(sensorData, currentPosition);
    
    if (matchResult) {
      console.log('MLA匹配成功:', matchResult);
    }
  },

  /**
   * MLA节点匹配回调
   */
  onMlaNodeMatched(match) {
    this.setData({
      lastMatch: {
        type: match.type,
        confidence: (match.confidence * 100).toFixed(1),
        node: match.node
      }
    });

    this.showToast(`匹配到${match.node.description}`);
  },

  /**
   * 位置校正回调
   */
  onPositionCorrected(correctedPosition) {
    console.log('位置已校正:', correctedPosition);
    
    this.setData({
      position: {
        x: parseFloat(correctedPosition.x.toFixed(2)),
        y: parseFloat(correctedPosition.y.toFixed(2)), 
        z: parseFloat(correctedPosition.z.toFixed(2))
      }
    });

    // 重绘画布
    this.drawCanvas();
  },

  /**
   * 校准传感器
   */
  async calibrateSensors() {
    if (this.data.trackingStatus) {
      this.showToast('请先停止定位');
      return;
    }

    this.setData({
      isLoading: true,
      loadingText: '正在校准传感器，请保持设备静止...'
    });

    try {
      // 启动传感器进行校准
      this.sensorManager.startCollection();
      
      // 校准传感器
      const calibrationResult = await this.sensorManager.calibrateSensors(3000);
      
      // 停止传感器
      this.sensorManager.stopCollection();
      
      // 保存校准结果到全局数据
      app.globalData.calibrationParams.accelerometerBias = calibrationResult.accelerometerBias;
      app.globalData.calibrationParams.gyroscopeBias = calibrationResult.gyroscopeBias;
      
      this.setData({ isLoading: false });
      this.showToast('传感器校准完成');
    } catch (error) {
      console.error('传感器校准失败:', error);
      this.setData({ isLoading: false });
      this.showToast('校准失败，请重试');
    }
  },

  /**
   * 重置轨迹
   */
  resetTracking() {
    if (this.data.trackingStatus) {
      this.showToast('请先停止定位');
      return;
    }

    this.resetTrackingData();
    this.showToast('轨迹已重置');
  },

  /**
   * 请求位置权限
   */
  async requestLocationPermission() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation']) {
            resolve();
          } else {
            wx.authorize({
              scope: 'scope.userLocation',
              success: resolve,
              fail: () => {
                wx.showModal({
                  title: '位置权限',
                  content: '需要位置权限进行室内定位',
                  success: (res) => {
                    if (res.confirm) {
                      wx.openSetting({
                        success: resolve,
                        fail: reject
                      });
                    } else {
                      reject(new Error('位置权限被拒绝'));
                    }
                  }
                });
              }
            });
          }
        },
        fail: reject
      });
    });
  },

  /**
   * UI事件处理
   */
  toggleDetails() {
    this.setData({
      showDetails: !this.data.showDetails
    });
  },

  showToast(message) {
    this.setData({
      showToast: true,
      toastMessage: message
    });
    
    setTimeout(() => {
      this.setData({ showToast: false });
    }, 2000);
  },

  hideToast() {
    this.setData({ showToast: false });
  },

  // 画布事件处理
  onCanvasTouchStart(e) {
    this.touchStartX = e.touches[0].x;
    this.touchStartY = e.touches[0].y;
  },

  onCanvasTouchMove(e) {
    if (this.touchStartX && this.touchStartY) {
      const deltaX = e.touches[0].x - this.touchStartX;
      const deltaY = e.touches[0].y - this.touchStartY;
      
      this.setData({
        offsetX: this.data.offsetX + deltaX,
        offsetY: this.data.offsetY + deltaY
      });
      
      this.drawCanvas();
      
      this.touchStartX = e.touches[0].x;
      this.touchStartY = e.touches[0].y;
    }
  },

  onCanvasTouchEnd(e) {
    this.touchStartX = null;
    this.touchStartY = null;
  },

  // 画布控制
  resetView() {
    this.setData({
      scale: 10,
      offsetX: 0,
      offsetY: 0
    });
    this.drawCanvas();
  },

  centerView() {
    const position = this.data.position;
    this.setData({
      offsetX: -position.x * this.data.scale,
      offsetY: position.y * this.data.scale
    });
    this.drawCanvas();
  },

  toggleGrid() {
    this.drawCanvas();
  },

  /**
   * 清理资源
   */
  cleanup() {
    if (this.sensorManager) {
      this.sensorManager.stopCollection();
    }
    console.log('页面资源已清理');
  }
});