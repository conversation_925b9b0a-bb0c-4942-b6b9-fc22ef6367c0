/**
 * 室内惯导定位微信小程序
 * 基于MLA-MFL算法实现
 * 武汉大学移动地理空间大数据云服务创新团队
 */

// 导入错误处理器
import { globalErrorHandler } from './utils/ErrorHandler.js';

App({
  onLaunch: function () {
    console.log('室内惯导定位系统启动');

    // 初始化全局错误处理器
    try {
      globalErrorHandler.init();
      console.log('全局错误处理器已启动');
    } catch (error) {
      console.warn('错误处理器初始化失败:', error);
    }

    // 检查微信版本
    if (wx.canIUse('getSetting')) {
      wx.getSetting({
        success: res => {
          if (res.authSetting['scope.userLocation']) {
            console.log('位置权限已获取');
          }
        }
      });
    }

    // 初始化全局数据
    this.globalData.pdrEngine = null;
    this.globalData.isTracking = false;
    this.globalData.sensorData = [];
    this.globalData.trackingPath = [];
    this.globalData.mlaNodes = [];
    this.globalData.errorHandler = globalErrorHandler;
  },

  onShow: function () {
    console.log('应用显示');
  },

  onHide: function () {
    console.log('应用隐藏');
    // 停止传感器数据收集
    this.stopSensorCollection();
  },

  globalData: {
    // PDR引擎实例
    pdrEngine: null,
    // 跟踪状态
    isTracking: false,
    // 传感器数据缓存
    sensorData: [],
    // 轨迹路径
    trackingPath: [],
    // MLA节点数据
    mlaNodes: [],
    // 校准参数
    calibrationParams: {
      stepLength: 0.75, // 默认步长
      magneticDeclination: 0, // 磁偏角
      accelerometerBias: [0, 0, 0], // 加速计偏置
      gyroscopeBias: [0, 0, 0] // 陀螺仪偏置
    },
    // 定位配置
    config: {
      sampleRate: 50, // 采样频率 Hz
      filterWindow: 10, // 滑动窗口大小
      stepThreshold: 0.2, // 步态检测阈值
      turnThreshold: 1.0, // 转向检测阈值
      mlaThreshold: 2.0 // MLA匹配距离阈值
    },
    // 当前位置信息
    currentPosition: {
      x: 0,
      y: 0,
      z: 0,
      floor: 1,
      heading: 0,
      timestamp: 0
    }
  },

  // 启动传感器数据收集
  startSensorCollection() {
    if (this.globalData.sensorCollecting) return;
    
    this.globalData.sensorCollecting = true;
    
    // 启动加速计
    wx.startAccelerometer({
      interval: 'fast',
      success: () => {
        console.log('加速计启动成功');
      },
      fail: (err) => {
        console.error('加速计启动失败:', err);
      }
    });

    // 启动陀螺仪
    wx.startGyroscope({
      interval: 'fast', 
      success: () => {
        console.log('陀螺仪启动成功');
      },
      fail: (err) => {
        console.error('陀螺仪启动失败:', err);
      }
    });

    // 启动罗盘
    wx.startCompass({
      success: () => {
        console.log('罗盘启动成功');
      },
      fail: (err) => {
        console.error('罗盘启动失败:', err);
      }
    });
  },

  // 停止传感器数据收集
  stopSensorCollection() {
    if (!this.globalData.sensorCollecting) return;
    
    this.globalData.sensorCollecting = false;
    
    wx.stopAccelerometer();
    wx.stopGyroscope();
    wx.stopCompass();
    
    console.log('传感器数据收集已停止');
  }
});