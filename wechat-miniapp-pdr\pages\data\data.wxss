/* 数据分析页面样式 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  padding-bottom: 140rpx; /* 为底部工具栏留空间 */
}

/* 头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 20rpx;
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
}

.export-btn {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 12rpx 24rpx;
}

.export-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.export-text {
  font-size: 24rpx;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.stat-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 24rpx;
}

.stat-icon.steps {
  background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
}

.stat-icon.distance {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.stat-icon.accuracy {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.stat-icon.time {
  background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 性能指标部分 */
.performance-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.refresh-btn {
  font-size: 32rpx;
  color: #667eea;
  cursor: pointer;
}

.performance-items {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.performance-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.perf-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.perf-value {
  font-size: 24rpx;
  color: #666;
}

.perf-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.perf-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #45a049 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.perf-fill.warning {
  background: linear-gradient(90deg, #FF9800 0%, #f57c00 100%);
}

/* 轨迹分析部分 */
.trajectory-section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.view-toggle {
  display: flex;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 4rpx;
}

.toggle-btn {
  flex: 1;
  text-align: center;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background: #667eea;
  color: white;
}

.trajectory-canvas-container {
  position: relative;
}

.trajectory-canvas {
  width: 100%;
  height: 300rpx;
  display: block;
}

.canvas-info {
  position: absolute;
  bottom: 16rpx;
  left: 16rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.info-text {
  display: block;
  margin-bottom: 4rpx;
}

.info-text:last-child {
  margin-bottom: 0;
}

/* 传感器状态部分 */
.sensor-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.sensor-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.sensor-card {
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
}

.sensor-card.acc {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.sensor-card.gyro {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.sensor-card.compass {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
}

.sensor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.sensor-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.sensor-status {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  font-size: 20rpx;
}

.sensor-status.active {
  color: #4CAF50;
}

.sensor-status.inactive {
  color: #f44336;
}

.sensor-data {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sensor-value {
  font-size: 24rpx;
  color: #666;
  font-family: 'Courier New', monospace;
}

.sensor-chart {
  width: 120rpx;
  height: 60rpx;
}

.mini-chart {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.compass-display {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #fff;
  border: 2rpx solid #ddd;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.compass-needle {
  width: 2rpx;
  height: 40rpx;
  background: #f44336;
  transform-origin: bottom;
  transition: transform 0.3s ease;
}

/* MLA匹配历史 */
.mla-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.clear-btn {
  font-size: 24rpx;
  color: #f44336;
  cursor: pointer;
}

.mla-history {
  max-height: 400rpx;
}

.mla-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.mla-item:last-child {
  border-bottom: none;
}

.mla-type {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  color: white;
  margin-right: 16rpx;
  min-width: 80rpx;
  text-align: center;
}

.mla-type.gyro {
  background: #2196F3;
}

.mla-type.pressure {
  background: #9C27B0;
}

.mla-type.accelerometer {
  background: #FF9800;
}

.mla-info {
  flex: 1;
  margin-right: 16rpx;
}

.mla-desc {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.mla-time {
  font-size: 22rpx;
  color: #999;
}

.mla-confidence {
  width: 120rpx;
  text-align: right;
}

.confidence-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.confidence-bar {
  height: 6rpx;
  background: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #45a049 100%);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 详情部分 */
.details-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.close-btn {
  font-size: 32rpx;
  color: #666;
  cursor: pointer;
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.detail-group {
  border-left: 4rpx solid #667eea;
  padding-left: 20rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

/* 底部工具栏 */
.bottom-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #e0e0e0;
  padding: 20rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.1);
}

.tool-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background: transparent;
  border: none;
  padding: 16rpx;
  min-width: 120rpx;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-label {
  font-size: 22rpx;
  color: #666;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: white;
  font-size: 28rpx;
}

/* 提示信息 */
.toast {
  position: fixed;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1001;
}

.toast.show {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .sensor-data {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }
  
  .bottom-toolbar {
    padding: 16rpx;
  }
  
  .tool-btn {
    min-width: 100rpx;
    padding: 12rpx;
  }
}