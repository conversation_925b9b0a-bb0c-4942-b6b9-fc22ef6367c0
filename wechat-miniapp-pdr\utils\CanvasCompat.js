/**
 * Canvas兼容性工具类
 * 用于处理Canvas 2D API和传统Canvas API的差异
 */

class CanvasCompat {
  constructor(isCanvas2D = false) {
    this.isCanvas2D = isCanvas2D;
  }

  /**
   * 设置Canvas样式属性
   * @param {CanvasRenderingContext2D} ctx Canvas上下文
   * @param {string} property 属性名
   * @param {any} value 属性值
   */
  setStyle(ctx, property, value) {
    if (this.isCanvas2D) {
      // Canvas 2D API - 直接设置属性
      ctx[property] = value;
    } else {
      // 传统API - 使用set方法
      const methodName = 'set' + property.charAt(0).toUpperCase() + property.slice(1);
      if (ctx[methodName]) {
        ctx[methodName](value);
      }
    }
  }

  /**
   * 设置填充样式
   */
  setFillStyle(ctx, color) {
    this.setStyle(ctx, 'fillStyle', color);
  }

  /**
   * 设置描边样式
   */
  setStrokeStyle(ctx, color) {
    this.setStyle(ctx, 'strokeStyle', color);
  }

  /**
   * 设置线宽
   */
  setLineWidth(ctx, width) {
    this.setStyle(ctx, 'lineWidth', width);
  }

  /**
   * 设置字体
   */
  setFont(ctx, font) {
    this.setStyle(ctx, 'font', font);
  }

  /**
   * 设置文本对齐
   */
  setTextAlign(ctx, align) {
    this.setStyle(ctx, 'textAlign', align);
  }

  /**
   * 设置文本基线
   */
  setTextBaseline(ctx, baseline) {
    this.setStyle(ctx, 'textBaseline', baseline);
  }

  /**
   * 设置全局透明度
   */
  setGlobalAlpha(ctx, alpha) {
    this.setStyle(ctx, 'globalAlpha', alpha);
  }

  /**
   * 设置线条端点样式
   */
  setLineCap(ctx, cap) {
    this.setStyle(ctx, 'lineCap', cap);
  }

  /**
   * 设置线条连接样式
   */
  setLineJoin(ctx, join) {
    this.setStyle(ctx, 'lineJoin', join);
  }

  /**
   * 绘制文本
   */
  fillText(ctx, text, x, y, maxWidth) {
    if (this.isCanvas2D) {
      ctx.fillText(text, x, y, maxWidth);
    } else {
      if (maxWidth !== undefined) {
        ctx.fillText(text, x, y, maxWidth);
      } else {
        ctx.fillText(text, x, y);
      }
    }
  }

  /**
   * 描边文本
   */
  strokeText(ctx, text, x, y, maxWidth) {
    if (this.isCanvas2D) {
      ctx.strokeText(text, x, y, maxWidth);
    } else {
      if (maxWidth !== undefined) {
        ctx.strokeText(text, x, y, maxWidth);
      } else {
        ctx.strokeText(text, x, y);
      }
    }
  }

  /**
   * 绘制圆弧
   */
  arc(ctx, x, y, radius, startAngle, endAngle, anticlockwise = false) {
    ctx.arc(x, y, radius, startAngle, endAngle, anticlockwise);
  }

  /**
   * 绘制矩形
   */
  rect(ctx, x, y, width, height) {
    ctx.rect(x, y, width, height);
  }

  /**
   * 移动到指定点
   */
  moveTo(ctx, x, y) {
    ctx.moveTo(x, y);
  }

  /**
   * 画线到指定点
   */
  lineTo(ctx, x, y) {
    ctx.lineTo(x, y);
  }

  /**
   * 开始路径
   */
  beginPath(ctx) {
    ctx.beginPath();
  }

  /**
   * 关闭路径
   */
  closePath(ctx) {
    ctx.closePath();
  }

  /**
   * 填充路径
   */
  fill(ctx) {
    ctx.fill();
  }

  /**
   * 描边路径
   */
  stroke(ctx) {
    ctx.stroke();
  }

  /**
   * 清除矩形区域
   */
  clearRect(ctx, x, y, width, height) {
    ctx.clearRect(x, y, width, height);
  }

  /**
   * 填充矩形
   */
  fillRect(ctx, x, y, width, height) {
    ctx.fillRect(x, y, width, height);
  }

  /**
   * 描边矩形
   */
  strokeRect(ctx, x, y, width, height) {
    ctx.strokeRect(x, y, width, height);
  }

  /**
   * 保存状态
   */
  save(ctx) {
    ctx.save();
  }

  /**
   * 恢复状态
   */
  restore(ctx) {
    ctx.restore();
  }

  /**
   * 平移
   */
  translate(ctx, x, y) {
    ctx.translate(x, y);
  }

  /**
   * 旋转
   */
  rotate(ctx, angle) {
    ctx.rotate(angle);
  }

  /**
   * 缩放
   */
  scale(ctx, x, y) {
    ctx.scale(x, y);
  }

  /**
   * 提交绘制（仅传统API需要）
   */
  draw(ctx) {
    if (!this.isCanvas2D && ctx.draw) {
      ctx.draw();
    }
  }

  /**
   * 测量文本宽度
   */
  measureText(ctx, text) {
    if (ctx.measureText) {
      return ctx.measureText(text);
    }
    // 简单估算
    return { width: text.length * 12 };
  }

  /**
   * 创建线性渐变
   */
  createLinearGradient(ctx, x0, y0, x1, y1) {
    if (ctx.createLinearGradient) {
      return ctx.createLinearGradient(x0, y0, x1, y1);
    }
    return null;
  }

  /**
   * 创建径向渐变
   */
  createRadialGradient(ctx, x0, y0, r0, x1, y1, r1) {
    if (ctx.createRadialGradient) {
      return ctx.createRadialGradient(x0, y0, r0, x1, y1, r1);
    }
    return null;
  }

  /**
   * 设置阴影
   */
  setShadow(ctx, offsetX, offsetY, blur, color) {
    if (this.isCanvas2D) {
      ctx.shadowOffsetX = offsetX;
      ctx.shadowOffsetY = offsetY;
      ctx.shadowBlur = blur;
      ctx.shadowColor = color;
    } else {
      if (ctx.setShadow) {
        ctx.setShadow(offsetX, offsetY, blur, color);
      }
    }
  }

  /**
   * 绘制图像
   */
  drawImage(ctx, image, ...args) {
    if (ctx.drawImage) {
      ctx.drawImage(image, ...args);
    }
  }
}

export default CanvasCompat;
