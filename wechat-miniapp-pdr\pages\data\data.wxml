<!--数据分析页面-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <text class="title">数据分析</text>
    <view class="export-btn" bindtap="exportData">
      <text class="export-icon">📤</text>
      <text class="export-text">导出</text>
    </view>
  </view>

  <!-- 统计卡片组 -->
  <view class="stats-grid">
    <view class="stat-card">
      <view class="stat-icon steps">👣</view>
      <view class="stat-info">
        <text class="stat-value">{{statistics.totalSteps}}</text>
        <text class="stat-label">总步数</text>
      </view>
    </view>
    
    <view class="stat-card">
      <view class="stat-icon distance">📏</view>
      <view class="stat-info">
        <text class="stat-value">{{statistics.totalDistance}}m</text>
        <text class="stat-label">总距离</text>
      </view>
    </view>
    
    <view class="stat-card">
      <view class="stat-icon accuracy">🎯</view>
      <view class="stat-info">
        <text class="stat-value">{{statistics.accuracy}}%</text>
        <text class="stat-label">定位精度</text>
      </view>
    </view>
    
    <view class="stat-card">
      <view class="stat-icon time">⏱️</view>
      <view class="stat-info">
        <text class="stat-value">{{statistics.trackingTimeFormatted}}</text>
        <text class="stat-label">跟踪时间</text>
      </view>
    </view>
  </view>

  <!-- 性能指标 -->
  <view class="performance-section">
    <view class="section-header">
      <text class="section-title">性能指标</text>
      <text class="refresh-btn" bindtap="refreshData">🔄</text>
    </view>
    
    <view class="performance-items">
      <view class="performance-item">
        <text class="perf-label">平均步长</text>
        <text class="perf-value">{{statistics.averageStepLength}}m</text>
        <view class="perf-bar">
          <view class="perf-fill" style="width: {{statistics.stepLengthPercentage}}%"></view>
        </view>
      </view>
      
      <view class="performance-item">
        <text class="perf-label">MLA匹配率</text>
        <text class="perf-value">{{statistics.matchRate}}%</text>
        <view class="perf-bar">
          <view class="perf-fill" style="width: {{statistics.matchRate}}%"></view>
        </view>
      </view>
      
      <view class="performance-item">
        <text class="perf-label">位置校正率</text>
        <text class="perf-value">{{statistics.correctionRate}}%</text>
        <view class="perf-bar">
          <view class="perf-fill" style="width: {{statistics.correctionRate}}%"></view>
        </view>
      </view>
      
      <view class="performance-item">
        <text class="perf-label">处理延迟</text>
        <text class="perf-value">{{statistics.averageProcessingTime}}ms</text>
        <view class="perf-bar">
          <view class="perf-fill warning" style="width: {{statistics.processingTimePercentage}}%"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 轨迹分析 -->
  <view class="trajectory-section">
    <view class="section-header">
      <text class="section-title">轨迹分析</text>
      <view class="view-toggle">
        <text class="toggle-btn {{trajectoryView === '2d' ? 'active' : ''}}" bindtap="setTrajectoryView" data-view="2d">2D</text>
        <text class="toggle-btn {{trajectoryView === '3d' ? 'active' : ''}}" bindtap="setTrajectoryView" data-view="3d">3D</text>
      </view>
    </view>
    
    <view class="trajectory-canvas-container">
      <canvas 
        class="trajectory-canvas" 
        canvas-id="trajectoryCanvas"
        bindtouchstart="onCanvasTouchStart"
        bindtouchmove="onCanvasTouchMove">
      </canvas>
      
      <view class="canvas-info">
        <text class="info-text">轨迹点数: {{trajectoryData.length}}</text>
        <text class="info-text">覆盖范围: {{trajectoryBounds.width}}×{{trajectoryBounds.height}}m</text>
      </view>
    </view>
  </view>

  <!-- 传感器状态 -->
  <view class="sensor-section">
    <view class="section-header">
      <text class="section-title">传感器状态</text>
    </view>
    
    <view class="sensor-grid">
      <view class="sensor-card acc">
        <view class="sensor-header">
          <text class="sensor-name">加速计</text>
          <view class="sensor-status {{sensorStatus.accelerometer}}">●</view>
        </view>
        <view class="sensor-data">
          <text class="sensor-value">{{currentSensorData.accelerometer.magnitude}} m/s²</text>
          <view class="sensor-chart">
            <canvas class="mini-chart" canvas-id="accChart"></canvas>
          </view>
        </view>
      </view>
      
      <view class="sensor-card gyro">
        <view class="sensor-header">
          <text class="sensor-name">陀螺仪</text>
          <view class="sensor-status {{sensorStatus.gyroscope}}">●</view>
        </view>
        <view class="sensor-data">
          <text class="sensor-value">{{currentSensorData.gyroscope.magnitude}} rad/s</text>
          <view class="sensor-chart">
            <canvas class="mini-chart" canvas-id="gyroChart"></canvas>
          </view>
        </view>
      </view>
      
      <view class="sensor-card compass">
        <view class="sensor-header">
          <text class="sensor-name">罗盘</text>
          <view class="sensor-status {{sensorStatus.compass}}">●</view>
        </view>
        <view class="sensor-data">
          <text class="sensor-value">{{currentSensorData.compass.direction}}°</text>
          <view class="compass-display">
            <view class="compass-needle" style="transform: rotate({{currentSensorData.compass.direction}}deg)"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- MLA匹配历史 -->
  <view class="mla-section">
    <view class="section-header">
      <text class="section-title">MLA匹配历史</text>
      <text class="clear-btn" bindtap="clearMlaHistory">清空</text>
    </view>
    
    <scroll-view class="mla-history" scroll-y="true">
      <view class="mla-item" wx:for="{{mlaHistory}}" wx:key="timestamp">
        <view class="mla-type {{item.type}}">{{item.type}}</view>
        <view class="mla-info">
          <text class="mla-desc">{{item.node.description}}</text>
          <text class="mla-time">{{item.formattedTime}}</text>
        </view>
        <view class="mla-confidence">
          <text class="confidence-value">{{item.confidence}}%</text>
          <view class="confidence-bar">
            <view class="confidence-fill" style="width: {{item.confidence}}%"></view>
          </view>
        </view>
      </view>
      
      <view class="empty-state" wx:if="{{mlaHistory.length === 0}}">
        <text class="empty-icon">📍</text>
        <text class="empty-text">暂无MLA匹配记录</text>
      </view>
    </scroll-view>
  </view>

  <!-- 数据详情 -->
  <view class="details-section" wx:if="{{showDetails}}">
    <view class="section-header">
      <text class="section-title">详细数据</text>
      <text class="close-btn" bindtap="hideDetails">✕</text>
    </view>
    
    <view class="details-content">
      <view class="detail-group">
        <text class="group-title">位置信息</text>
        <view class="detail-item">
          <text class="detail-label">当前位置:</text>
          <text class="detail-value">({{currentPosition.x}}, {{currentPosition.y}}, {{currentPosition.z}})</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">当前航向:</text>
          <text class="detail-value">{{currentHeading}}°</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">当前楼层:</text>
          <text class="detail-value">{{currentFloor}}F</text>
        </view>
      </view>
      
      <view class="detail-group">
        <text class="group-title">算法参数</text>
        <view class="detail-item">
          <text class="detail-label">PDR权重:</text>
          <text class="detail-value">{{fusionParams.pdrWeight}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">MLA权重:</text>
          <text class="detail-value">{{fusionParams.mlaWeight}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">置信度阈值:</text>
          <text class="detail-value">{{fusionParams.confidenceThreshold}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-toolbar">
    <button class="tool-btn" bindtap="showDetails">
      <text class="btn-icon">📊</text>
      <text class="btn-label">详情</text>
    </button>
    <button class="tool-btn" bindtap="shareData">
      <text class="btn-icon">📤</text>
      <text class="btn-label">分享</text>
    </button>
    <button class="tool-btn" bindtap="clearAllData">
      <text class="btn-icon">🗑️</text>
      <text class="btn-label">清空</text>
    </button>
  </view>

  <!-- 加载指示器 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>

  <!-- 提示信息 -->
  <view class="toast {{showToast ? 'show' : ''}}" bindtap="hideToast">
    <text>{{toastMessage}}</text>
  </view>
</view>