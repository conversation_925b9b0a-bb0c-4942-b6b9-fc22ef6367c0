// 传感器校准页面
import SensorManager from '../../utils/SensorManager.js';

const app = getApp();

Page({
  data: {
    // 校准状态
    calibrationStatus: 'pending', // pending, success, error
    statusText: '未校准',
    isCalibrating: false,
    progress: 0,
    progressText: '准备校准...',

    // 各传感器校准状态
    accelerometerStatus: 'pending', // pending, success, error
    gyroscopeStatus: 'pending',
    compassStatus: 'pending',
    
    // 状态图标
    accelerometerStatusIcon: '⏳',
    gyroscopeStatusIcon: '⏳',
    compassStatusIcon: '⏳',

    // 校准结果
    accBias: { x: 0, y: 0, z: 0 },
    gyroBias: { x: 0, y: 0, z: 0 },
    magneticDeclination: 0,
    compassAccuracy: '未知',
    compassSamples: 0,

    // UI状态
    showAccDetails: false,
    showGyroDetails: false,
    showCompassDetails: false,
    showGuidance: true,
    showAnimation: false,
    animationType: '',
    animationText: '',
    hasValidCalibration: false,

    // 提示信息
    showToast: false,
    toastMessage: ''
  },

  // 传感器管理器
  sensorManager: null,
  calibrationTimer: null,

  onLoad: function (options) {
    console.log('传感器校准页面加载');
    this.initializeSensorManager();
    this.loadSavedCalibration();
  },

  onShow: function () {
    this.checkCalibrationStatus();
  },

  onHide: function () {
    this.stopCalibration();
  },

  onUnload: function () {
    this.cleanup();
  },

  /**
   * 初始化传感器管理器
   */
  initializeSensorManager() {
    this.sensorManager = new SensorManager();
    this.sensorManager.setCallbacks({
      onError: this.onSensorError.bind(this)
    });
  },

  /**
   * 加载已保存的校准数据
   */
  loadSavedCalibration() {
    try {
      const calibrationData = wx.getStorageSync('calibrationData');
      if (calibrationData) {
        this.setData({
          accBias: calibrationData.accelerometerBias || { x: 0, y: 0, z: 0 },
          gyroBias: calibrationData.gyroscopeBias || { x: 0, y: 0, z: 0 },
          magneticDeclination: calibrationData.magneticDeclination || 0,
          compassAccuracy: calibrationData.compassAccuracy || '未知',
          compassSamples: calibrationData.compassSamples || 0
        });
        
        this.updateCalibrationStatus();
      }
    } catch (error) {
      console.error('加载校准数据失败:', error);
    }
  },

  /**
   * 检查校准状态
   */
  checkCalibrationStatus() {
    const { accBias, gyroBias, magneticDeclination } = this.data;
    
    // 检查加速计校准
    const accCalibrated = Math.abs(accBias.x) > 0.01 || Math.abs(accBias.y) > 0.01 || Math.abs(accBias.z) > 0.01;
    this.setData({
      accelerometerStatus: accCalibrated ? 'success' : 'pending',
      accelerometerStatusIcon: accCalibrated ? '✅' : '⏳'
    });

    // 检查陀螺仪校准
    const gyroCalibrated = Math.abs(gyroBias.x) > 0.001 || Math.abs(gyroBias.y) > 0.001 || Math.abs(gyroBias.z) > 0.001;
    this.setData({
      gyroscopeStatus: gyroCalibrated ? 'success' : 'pending',
      gyroscopeStatusIcon: gyroCalibrated ? '✅' : '⏳'
    });

    // 检查罗盘校准
    const compassCalibrated = Math.abs(magneticDeclination) > 0.1;
    this.setData({
      compassStatus: compassCalibrated ? 'success' : 'pending',
      compassStatusIcon: compassCalibrated ? '✅' : '⏳'
    });

    // 更新总体状态
    this.updateCalibrationStatus();
  },

  /**
   * 更新校准状态
   */
  updateCalibrationStatus() {
    const { accelerometerStatus, gyroscopeStatus, compassStatus } = this.data;
    
    let overallStatus = 'pending';
    let statusText = '未校准';
    let hasValidCalibration = false;
    
    const successCount = [accelerometerStatus, gyroscopeStatus, compassStatus]
      .filter(status => status === 'success').length;
    
    if (successCount === 3) {
      overallStatus = 'success';
      statusText = '校准完成';
      hasValidCalibration = true;
    } else if (successCount > 0) {
      overallStatus = 'partial';
      statusText = `已校准 ${successCount}/3`;
      hasValidCalibration = true;
    }
    
    this.setData({
      calibrationStatus: overallStatus,
      statusText: statusText,
      hasValidCalibration: hasValidCalibration
    });
  },

  /**
   * 校准加速计
   */
  async calibrateAccelerometer() {
    if (this.data.isCalibrating) return;
    
    this.setData({
      isCalibrating: true,
      progress: 0,
      progressText: '准备校准加速计...',
      showAnimation: true,
      animationType: 'static',
      animationText: '请将设备平放在水平面上，保持静止'
    });

    try {
      // 启动传感器
      this.sensorManager.startCollection();
      
      // 显示倒计时
      await this.showCountdown(3);
      
      // 执行校准
      this.setData({
        progressText: '正在校准加速计...'
      });
      
      const result = await this.sensorManager.calibrateSensors(3000);
      
      // 更新校准结果
      this.setData({
        accBias: {
          x: parseFloat(result.accelerometerBias[0].toFixed(3)),
          y: parseFloat(result.accelerometerBias[1].toFixed(3)),
          z: parseFloat(result.accelerometerBias[2].toFixed(3))
        },
        accelerometerStatus: 'success',
        accelerometerStatusIcon: '✅',
        progress: 100,
        progressText: '加速计校准完成'
      });
      
      this.showToast('加速计校准成功');
      this.updateCalibrationStatus();
      this.saveCalibrationData();
      
    } catch (error) {
      console.error('加速计校准失败:', error);
      this.setData({
        accelerometerStatus: 'error',
        accelerometerStatusIcon: '❌'
      });
      this.showToast('加速计校准失败，请重试');
    } finally {
      this.sensorManager.stopCollection();
      this.setData({
        isCalibrating: false,
        showAnimation: false,
        progress: 0
      });
    }
  },

  /**
   * 校准陀螺仪
   */
  async calibrateGyroscope() {
    if (this.data.isCalibrating) return;
    
    this.setData({
      isCalibrating: true,
      progress: 0,
      progressText: '准备校准陀螺仪...',
      showAnimation: true,
      animationType: 'static',
      animationText: '请保持设备完全静止，避免任何旋转'
    });

    try {
      this.sensorManager.startCollection();
      await this.showCountdown(3);
      
      this.setData({
        progressText: '正在校准陀螺仪...'
      });
      
      const result = await this.sensorManager.calibrateSensors(3000);
      
      this.setData({
        gyroBias: {
          x: parseFloat(result.gyroscopeBias[0].toFixed(4)),
          y: parseFloat(result.gyroscopeBias[1].toFixed(4)),
          z: parseFloat(result.gyroscopeBias[2].toFixed(4))
        },
        gyroscopeStatus: 'success',
        gyroscopeStatusIcon: '✅',
        progress: 100,
        progressText: '陀螺仪校准完成'
      });
      
      this.showToast('陀螺仪校准成功');
      this.updateCalibrationStatus();
      this.saveCalibrationData();
      
    } catch (error) {
      console.error('陀螺仪校准失败:', error);
      this.setData({
        gyroscopeStatus: 'error',
        gyroscopeStatusIcon: '❌'
      });
      this.showToast('陀螺仪校准失败，请重试');
    } finally {
      this.sensorManager.stopCollection();
      this.setData({
        isCalibrating: false,
        showAnimation: false,
        progress: 0
      });
    }
  },

  /**
   * 校准罗盘
   */
  async calibrateCompass() {
    if (this.data.isCalibrating) return;
    
    this.setData({
      isCalibrating: true,
      progress: 0,
      progressText: '准备校准罗盘...',
      showAnimation: true,
      animationType: 'figure8',
      animationText: '请按8字形缓慢旋转设备30秒'
    });

    try {
      this.sensorManager.startCollection();
      await this.showCountdown(3);
      
      this.setData({
        progressText: '正在校准罗盘，请按8字形旋转设备...'
      });
      
      // 模拟罗盘校准过程
      const compassData = [];
      const duration = 30000; // 30秒
      const startTime = Date.now();
      
      const collectCompassData = () => {
        return new Promise((resolve) => {
          const interval = setInterval(() => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min((elapsed / duration) * 100, 100);
            
            this.setData({
              progress: progress,
              progressText: `罗盘校准中... ${Math.round(progress)}%`
            });
            
            // 模拟收集到的样本数量
            const samples = Math.floor(progress * 10);
            this.setData({
              compassSamples: samples
            });
            
            if (elapsed >= duration) {
              clearInterval(interval);
              resolve();
            }
          }, 100);
        });
      };
      
      await collectCompassData();
      
      // 计算磁偏角（模拟）
      const magneticDeclination = (Math.random() - 0.5) * 20; // -10° to +10°
      
      this.setData({
        magneticDeclination: parseFloat(magneticDeclination.toFixed(1)),
        compassAccuracy: '良好',
        compassStatus: 'success',
        compassStatusIcon: '✅',
        progress: 100,
        progressText: '罗盘校准完成'
      });
      
      this.showToast('罗盘校准成功');
      this.updateCalibrationStatus();
      this.saveCalibrationData();
      
    } catch (error) {
      console.error('罗盘校准失败:', error);
      this.setData({
        compassStatus: 'error',
        compassStatusIcon: '❌'
      });
      this.showToast('罗盘校准失败，请重试');
    } finally {
      this.sensorManager.stopCollection();
      this.setData({
        isCalibrating: false,
        showAnimation: false,
        progress: 0
      });
    }
  },

  /**
   * 一键校准所有传感器
   */
  async quickCalibration() {
    if (this.data.isCalibrating) return;
    
    this.setData({
      progressText: '开始一键校准...'
    });
    
    try {
      await this.calibrateAccelerometer();
      await this.sleep(1000);
      
      await this.calibrateGyroscope();
      await this.sleep(1000);
      
      await this.calibrateCompass();
      
      this.showToast('所有传感器校准完成');
    } catch (error) {
      console.error('一键校准失败:', error);
      this.showToast('一键校准失败，请分别校准');
    }
  },

  /**
   * 显示倒计时
   */
  async showCountdown(seconds) {
    for (let i = seconds; i > 0; i--) {
      this.setData({
        progressText: `准备中... ${i}秒`,
        progress: ((seconds - i) / seconds) * 30
      });
      await this.sleep(1000);
    }
  },

  /**
   * 延时函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * 保存校准数据
   */
  saveCalibrationData() {
    try {
      const calibrationData = {
        accelerometerBias: [this.data.accBias.x, this.data.accBias.y, this.data.accBias.z],
        gyroscopeBias: [this.data.gyroBias.x, this.data.gyroBias.y, this.data.gyroBias.z],
        magneticDeclination: this.data.magneticDeclination,
        compassAccuracy: this.data.compassAccuracy,
        compassSamples: this.data.compassSamples,
        calibrationTime: Date.now()
      };
      
      wx.setStorageSync('calibrationData', calibrationData);
      
      // 同步到全局数据
      app.globalData.calibrationParams = {
        ...app.globalData.calibrationParams,
        ...calibrationData
      };
      
      console.log('校准数据已保存');
    } catch (error) {
      console.error('保存校准数据失败:', error);
    }
  },

  /**
   * 重置校准数据
   */
  resetCalibration() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有校准数据吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            accBias: { x: 0, y: 0, z: 0 },
            gyroBias: { x: 0, y: 0, z: 0 },
            magneticDeclination: 0,
            compassAccuracy: '未知',
            compassSamples: 0,
            accelerometerStatus: 'pending',
            gyroscopeStatus: 'pending',
            compassStatus: 'pending',
            accelerometerStatusIcon: '⏳',
            gyroscopeStatusIcon: '⏳',
            compassStatusIcon: '⏳'
          });
          
          this.updateCalibrationStatus();
          
          // 清除存储数据
          try {
            wx.removeStorageSync('calibrationData');
          } catch (error) {
            console.error('清除校准数据失败:', error);
          }
          
          this.showToast('校准数据已重置');
        }
      }
    });
  },

  /**
   * 保存并应用校准
   */
  saveCalibration() {
    if (!this.data.hasValidCalibration) {
      this.showToast('请先完成传感器校准');
      return;
    }
    
    this.saveCalibrationData();
    this.showToast('校准参数已保存并应用');
    
    // 返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },

  /**
   * 停止校准
   */
  stopCalibration() {
    if (this.calibrationTimer) {
      clearInterval(this.calibrationTimer);
      this.calibrationTimer = null;
    }
    
    if (this.sensorManager) {
      this.sensorManager.stopCollection();
    }
    
    this.setData({
      isCalibrating: false,
      showAnimation: false,
      progress: 0
    });
  },

  /**
   * 传感器错误回调
   */
  onSensorError(sensorType, error) {
    console.error(`传感器错误 [${sensorType}]:`, error);
    this.showToast(`${sensorType}传感器异常`);
  },

  /**
   * UI事件处理
   */
  toggleAccDetails() {
    this.setData({
      showAccDetails: !this.data.showAccDetails
    });
  },

  toggleGyroDetails() {
    this.setData({
      showGyroDetails: !this.data.showGyroDetails
    });
  },

  toggleCompassDetails() {
    this.setData({
      showCompassDetails: !this.data.showCompassDetails
    });
  },

  hideGuidance() {
    this.setData({
      showGuidance: false
    });
  },

  showToast(message) {
    this.setData({
      showToast: true,
      toastMessage: message
    });
    
    setTimeout(() => {
      this.setData({ showToast: false });
    }, 2000);
  },

  hideToast() {
    this.setData({ showToast: false });
  },

  /**
   * 清理资源
   */
  cleanup() {
    this.stopCalibration();
    if (this.sensorManager) {
      this.sensorManager.stopCollection();
    }
    console.log('校准页面资源已清理');
  }
});