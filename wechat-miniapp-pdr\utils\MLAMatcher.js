/**
 * MLA节点匹配器
 * Multi-sensor Landmark Association 多传感器地标关联
 */

class MLAMatcher {
  constructor() {
    // MLA节点数据库
    this.mlaDatabase = {
      gyro: [], // 陀螺仪节点（转弯点）
      pressure: [], // 气压计节点（楼层变化点）
      accelerometer: [], // 加速计节点（门节点、特殊区域）
      floor: [] // 楼梯台阶节点
    };

    // 匹配参数
    this.config = {
      maxMatchDistance: 2.0, // 最大匹配距离(米)
      gyroTurnThreshold: 1.0, // 陀螺仪转弯阈值
      pressureFloorThreshold: 2.0, // 气压楼层变化阈值(hPa)
      accelerometerVarianceThreshold: 0.2, // 加速计方差阈值
      confidenceThreshold: 0.7 // 匹配置信度阈值
    };

    // 当前状态
    this.currentState = {
      floor: 4, // 当前楼层
      lastMatchedNode: null, // 上次匹配的节点
      position: { x: 0, y: 0, z: 0 }, // 当前PDR位置
      initialPosition: { x: -23.43, y: 13.5, z: 0 } // 初始坐标
    };

    // 匹配历史
    this.matchHistory = [];
    
    // 回调函数
    this.onNodeMatched = null;
    this.onPositionCorrected = null;
  }

  /**
   * 初始化MLA节点数据库
   */
  initializeMlaDatabase(mlaData) {
    if (mlaData) {
      this.mlaDatabase = { ...this.mlaDatabase, ...mlaData };
    } else {
      // 默认测试数据
      this.loadDefaultMlaNodes();
    }
    
    console.log('MLA数据库初始化完成');
    console.log('节点统计:', {
      gyro: this.mlaDatabase.gyro.length,
      pressure: this.mlaDatabase.pressure.length,
      accelerometer: this.mlaDatabase.accelerometer.length,
      floor: this.mlaDatabase.floor.length
    });
  }

  /**
   * 加载默认MLA节点（测试用）
   */
  loadDefaultMlaNodes() {
    // 陀螺仪节点（转弯点）
    this.mlaDatabase.gyro = [
      { id: 1, x: -45.13, y: 18, z: 0, floor: 5, description: '5楼走廊转弯点', sensors: 'gyro' },
      { id: 2, x: -23.43, y: 13.5, z: 0, floor: 4, description: '4楼走廊转弯点', sensors: 'gyro' },
      { id: 3, x: 5, y: -4, z: 18, floor: 5, description: '5楼楼梯口', sensors: 'gyro' }
    ];

    // 气压计节点（楼层变化点）
    this.mlaDatabase.pressure = [
      { id: 4, x: 0, y: 0, z: 13.5, floor: 4, description: '4楼气压参考点', sensors: 'pres' },
      { id: 5, x: 0, y: 0, z: 18, floor: 5, description: '5楼气压参考点', sensors: 'pres' }
    ];

    // 加速计节点（门节点）
    this.mlaDatabase.accelerometer = [
      { id: 6, x: 7, y: -1.5, z: 13.5, floor: 4, description: '4楼防火门', sensors: 'acc' },
      { id: 7, x: 7, y: -1.5, z: 18, floor: 5, description: '5楼防火门', sensors: 'acc' }
    ];

    // 楼梯台阶节点
    this.mlaDatabase.floor = [
      { id: 8, x: 5, y: -4, z: 18, floor: 5, description: '5楼楼梯最高点', sensors: 'floor' }
    ];
  }

  /**
   * 主匹配函数
   * @param {Object} sensorData 传感器数据
   * @param {Object} pdrPosition 当前PDR位置
   */
  matchNodes(sensorData, pdrPosition) {
    this.currentState.position = pdrPosition;
    
    const matchResults = [];

    // 陀螺仪节点匹配
    if (sensorData.gyroscope) {
      const gyroMatch = this.matchGyroscopeNodes(sensorData.gyroscope);
      if (gyroMatch) {
        matchResults.push(gyroMatch);
      }
    }

    // 气压计节点匹配
    if (sensorData.pressure) {
      const pressureMatch = this.matchPressureNodes(sensorData.pressure);
      if (pressureMatch) {
        matchResults.push(pressureMatch);
      }
    }

    // 加速计节点匹配
    if (sensorData.accelerometer) {
      const accMatch = this.matchAccelerometerNodes(sensorData.accelerometer);
      if (accMatch) {
        matchResults.push(accMatch);
      }
    }

    // 选择最佳匹配
    const bestMatch = this.selectBestMatch(matchResults);
    
    if (bestMatch) {
      this.processMatch(bestMatch);
    }

    return bestMatch;
  }

  /**
   * 陀螺仪节点匹配（转弯检测）
   */
  matchGyroscopeNodes(gyroData) {
    // 检测转弯
    const isTurning = this.detectTurn(gyroData);
    if (!isTurning) {
      return null;
    }

    // 获取当前楼层的陀螺仪节点
    const candidateNodes = this.mlaDatabase.gyro.filter(
      node => node.floor === this.currentState.floor
    );

    if (candidateNodes.length === 0) {
      return null;
    }

    // 计算距离并找到最近的节点
    const matches = candidateNodes.map(node => {
      const distance = this.calculateDistance(this.currentState.position, node);
      const confidence = this.calculateMatchConfidence('gyro', distance, gyroData);
      
      return {
        node: node,
        distance: distance,
        confidence: confidence,
        type: 'gyro',
        sensorData: gyroData
      };
    });

    // 选择最佳匹配
    const bestMatch = matches
      .filter(match => match.distance < this.config.maxMatchDistance)
      .filter(match => match.confidence > this.config.confidenceThreshold)
      .sort((a, b) => b.confidence - a.confidence)[0];

    return bestMatch || null;
  }

  /**
   * 气压计节点匹配（楼层变化检测）
   */
  matchPressureNodes(pressureData) {
    const currentPressure = pressureData.current;
    const initialPressure = pressureData.initial || currentPressure;
    
    // 检测楼层变化
    const pressureDiff = Math.abs(initialPressure - currentPressure);
    
    if (pressureDiff < this.config.pressureFloorThreshold) {
      return null;
    }

    // 判断上楼还是下楼
    const isGoingUp = initialPressure > currentPressure;
    const newFloor = isGoingUp ? this.currentState.floor + 1 : this.currentState.floor - 1;

    // 获取目标楼层的气压节点
    const candidateNodes = this.mlaDatabase.pressure.filter(
      node => node.floor === newFloor
    );

    if (candidateNodes.length === 0) {
      return null;
    }

    // 选择最近的气压节点
    const bestNode = candidateNodes[0]; // 通常每层只有一个气压参考点
    const confidence = Math.min(0.9, pressureDiff / 10.0); // 基于压差计算置信度

    return {
      node: bestNode,
      distance: 0, // 气压匹配不依赖距离
      confidence: confidence,
      type: 'pressure',
      sensorData: pressureData,
      floorChange: {
        from: this.currentState.floor,
        to: newFloor,
        direction: isGoingUp ? 'up' : 'down'
      }
    };
  }

  /**
   * 加速计节点匹配（门节点等特殊区域）
   */
  matchAccelerometerNodes(accData) {
    // 计算加速度方差
    const variance = this.calculateAccelerometerVariance(accData);
    
    // 检测特殊运动模式（如进门的走-停-走模式）
    const isSpecialPattern = variance > this.config.accelerometerVarianceThreshold;
    
    if (!isSpecialPattern) {
      return null;
    }

    // 获取当前楼层的加速计节点
    const candidateNodes = this.mlaDatabase.accelerometer.filter(
      node => node.floor === this.currentState.floor
    );

    if (candidateNodes.length === 0) {
      return null;
    }

    // 计算距离匹配
    const matches = candidateNodes.map(node => {
      const distance = this.calculateDistance(this.currentState.position, node);
      const confidence = this.calculateMatchConfidence('accelerometer', distance, accData);
      
      return {
        node: node,
        distance: distance,
        confidence: confidence,
        type: 'accelerometer',
        sensorData: accData
      };
    });

    // 选择最佳匹配
    const bestMatch = matches
      .filter(match => match.distance < this.config.maxMatchDistance)
      .filter(match => match.confidence > this.config.confidenceThreshold)
      .sort((a, b) => b.confidence - a.confidence)[0];

    return bestMatch || null;
  }

  /**
   * 检测转弯
   */
  detectTurn(gyroData) {
    if (!gyroData || !gyroData.z) {
      return false;
    }

    const gyroZ = Math.abs(gyroData.z);
    return gyroZ > this.config.gyroTurnThreshold;
  }

  /**
   * 计算加速计方差
   */
  calculateAccelerometerVariance(accDataArray) {
    if (!accDataArray || accDataArray.length < 10) {
      return 0;
    }

    const magnitudes = accDataArray.map(data => 
      Math.sqrt(data.x * data.x + data.y * data.y + data.z * data.z)
    );

    const mean = magnitudes.reduce((sum, val) => sum + val, 0) / magnitudes.length;
    const variance = magnitudes.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / magnitudes.length;

    return variance;
  }

  /**
   * 计算两点间距离
   */
  calculateDistance(pos1, pos2) {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  /**
   * 计算匹配置信度
   */
  calculateMatchConfidence(sensorType, distance, sensorData) {
    let baseConfidence = 0;
    
    // 基于距离的置信度
    const distanceConfidence = Math.max(0, 1 - distance / this.config.maxMatchDistance);
    
    switch (sensorType) {
      case 'gyro':
        // 基于角速度幅值的置信度
        const gyroMagnitude = Math.abs(sensorData.z || 0);
        baseConfidence = Math.min(1, gyroMagnitude / 2.0);
        break;
        
      case 'pressure':
        // 基于压差的置信度
        const pressureDiff = Math.abs(sensorData.current - sensorData.initial);
        baseConfidence = Math.min(1, pressureDiff / 5.0);
        break;
        
      case 'accelerometer':
        // 基于方差的置信度
        const variance = this.calculateAccelerometerVariance(sensorData);
        baseConfidence = Math.min(1, variance / 0.5);
        break;
        
      default:
        baseConfidence = 0.5;
    }

    // 综合置信度
    return (baseConfidence * 0.7 + distanceConfidence * 0.3);
  }

  /**
   * 选择最佳匹配
   */
  selectBestMatch(matchResults) {
    if (matchResults.length === 0) {
      return null;
    }

    // 按置信度排序
    const sortedMatches = matchResults
      .filter(match => match.confidence > this.config.confidenceThreshold)
      .sort((a, b) => b.confidence - a.confidence);

    return sortedMatches[0] || null;
  }

  /**
   * 处理匹配结果
   */
  processMatch(match) {
    console.log('MLA节点匹配成功:', match);
    
    // 位置校正
    const correctedPosition = this.correctPosition(match);
    
    // 更新状态
    this.currentState.lastMatchedNode = match.node;
    this.currentState.position = correctedPosition;
    
    // 楼层更新
    if (match.type === 'pressure' && match.floorChange) {
      this.currentState.floor = match.floorChange.to;
      console.log(`楼层变化: ${match.floorChange.from} -> ${match.floorChange.to}`);
    }

    // 记录匹配历史
    this.matchHistory.push({
      timestamp: Date.now(),
      match: match,
      correctedPosition: correctedPosition
    });

    // 维持历史记录长度
    if (this.matchHistory.length > 50) {
      this.matchHistory.shift();
    }

    // 触发回调
    if (this.onNodeMatched) {
      this.onNodeMatched(match);
    }

    if (this.onPositionCorrected) {
      this.onPositionCorrected(correctedPosition);
    }

    return correctedPosition;
  }

  /**
   * 位置校正
   */
  correctPosition(match) {
    const node = match.node;
    const currentPos = this.currentState.position;
    
    // 将MLA坐标转换为PDR坐标系
    const correctedPdrPos = this.mlaCoordinateToPdr(node, this.currentState.initialPosition);
    
    // 根据匹配类型和置信度进行校正
    let correctionWeight = match.confidence * 0.8;
    
    // 气压匹配的权重更高（楼层变化时）
    if (match.type === 'pressure') {
      correctionWeight = 0.9;
    }

    // 加权平均校正
    const correctedPosition = {
      x: currentPos.x * (1 - correctionWeight) + correctedPdrPos.x * correctionWeight,
      y: currentPos.y * (1 - correctionWeight) + correctedPdrPos.y * correctionWeight,
      z: currentPos.z * (1 - correctionWeight) + correctedPdrPos.z * correctionWeight
    };

    console.log('位置校正:', {
      原始位置: currentPos,
      匹配节点: { x: node.x, y: node.y, z: node.z },
      校正位置: correctedPosition,
      校正权重: correctionWeight
    });

    return correctedPosition;
  }

  /**
   * MLA坐标转PDR坐标
   */
  mlaCoordinateToPdr(mlaNode, initialPos) {
    return {
      x: mlaNode.x - initialPos.x,
      y: -(mlaNode.z - initialPos.z), // MLA的Z对应PDR的Y
      z: mlaNode.y - initialPos.y     // MLA的Y对应PDR的Z
    };
  }

  /**
   * PDR坐标转MLA坐标
   */
  pdrCoordinateToMla(pdrPos, initialPos) {
    return {
      x: initialPos.x + pdrPos.x,
      y: initialPos.y + pdrPos.z,
      z: initialPos.z - pdrPos.y
    };
  }

  /**
   * 设置初始位置
   */
  setInitialPosition(position) {
    this.currentState.initialPosition = position;
    console.log('设置初始位置:', position);
  }

  /**
   * 设置当前楼层
   */
  setCurrentFloor(floor) {
    this.currentState.floor = floor;
    console.log('设置当前楼层:', floor);
  }

  /**
   * 获取匹配历史
   */
  getMatchHistory() {
    return this.matchHistory;
  }

  /**
   * 获取当前状态
   */
  getCurrentState() {
    return { ...this.currentState };
  }

  /**
   * 设置配置参数
   */
  setConfig(config) {
    this.config = { ...this.config, ...config };
  }

  /**
   * 设置回调函数
   */
  setCallbacks(callbacks) {
    if (callbacks.onNodeMatched) {
      this.onNodeMatched = callbacks.onNodeMatched;
    }
    if (callbacks.onPositionCorrected) {
      this.onPositionCorrected = callbacks.onPositionCorrected;
    }
  }

  /**
   * 重置匹配器
   */
  reset() {
    this.currentState = {
      floor: 4,
      lastMatchedNode: null,
      position: { x: 0, y: 0, z: 0 },
      initialPosition: { x: -23.43, y: 13.5, z: 0 }
    };
    
    this.matchHistory = [];
    console.log('MLA匹配器已重置');
  }

  /**
   * 导出MLA数据库
   */
  exportMlaDatabase() {
    return JSON.stringify(this.mlaDatabase, null, 2);
  }

  /**
   * 导入MLA数据库
   */
  importMlaDatabase(jsonData) {
    try {
      const data = JSON.parse(jsonData);
      this.mlaDatabase = data;
      console.log('MLA数据库导入成功');
      return true;
    } catch (error) {
      console.error('MLA数据库导入失败:', error);
      return false;
    }
  }
}

export default MLAMatcher;