/**
 * PDR系统性能测试脚本
 * 用于验证优化效果
 */

// 模拟微信小程序环境
const mockWx = {
  startAccelerometer: (options) => {
    setTimeout(() => {
      if (Math.random() > 0.1) { // 90%成功率
        options.success && options.success();
      } else {
        options.fail && options.fail(new Error('加速计启动失败'));
      }
    }, Math.random() * 500 + 100); // 100-600ms随机延迟
  },
  
  startGyroscope: (options) => {
    setTimeout(() => {
      if (Math.random() > 0.1) {
        options.success && options.success();
      } else {
        options.fail && options.fail(new Error('陀螺仪启动失败'));
      }
    }, Math.random() * 800 + 200); // 200-1000ms随机延迟
  },
  
  startCompass: (options) => {
    setTimeout(() => {
      if (Math.random() > 0.2) { // 80%成功率，罗盘更容易失败
        options.success && options.success();
      } else {
        options.fail && options.fail(new Error('罗盘启动失败'));
      }
    }, Math.random() * 1200 + 300); // 300-1500ms随机延迟
  },
  
  onAccelerometerChange: () => {},
  onGyroscopeChange: () => {},
  onCompassChange: () => {},
  stopAccelerometer: () => {},
  stopGyroscope: () => {},
  stopCompass: () => {}
};

// 设置全局wx对象
global.wx = mockWx;

// 导入优化后的模块
const HeadingEstimator = require('../utils/HeadingEstimator.js');
const SensorManager = require('../utils/SensorManager.js');
const PerformanceMonitor = require('../utils/PerformanceMonitor.js');

/**
 * 测试航向角估计器性能
 */
async function testHeadingEstimator() {
  console.log('\n=== 测试航向角估计器 ===');
  
  const estimator = new HeadingEstimator();
  const monitor = new PerformanceMonitor();
  
  monitor.startMonitoring();
  monitor.startTimer('headingInit');
  
  // 测试快速启动
  const quickStartSuccess = estimator.quickStart(0);
  monitor.endTimer('headingInit');
  
  console.log('快速启动成功:', quickStartSuccess);
  console.log('初始化时间:', monitor.endTimer('headingInit') || 0, 'ms');
  
  // 测试更新性能
  monitor.startTimer('headingUpdate');
  const startTime = Date.now();
  
  for (let i = 0; i < 100; i++) {
    estimator.updateHeading({
      gyroZ: Math.random() * 0.1 - 0.05,
      compassHeading: Math.random() * 360
    }, Date.now());
  }
  
  const updateTime = Date.now() - startTime;
  monitor.endTimer('headingUpdate');
  
  console.log('100次更新总时间:', updateTime, 'ms');
  console.log('平均更新时间:', (updateTime / 100).toFixed(2), 'ms');
  
  return {
    quickStartSuccess,
    avgUpdateTime: updateTime / 100
  };
}

/**
 * 测试传感器管理器性能
 */
async function testSensorManager() {
  console.log('\n=== 测试传感器管理器 ===');
  
  const sensorManager = new SensorManager();
  const monitor = new PerformanceMonitor();
  
  monitor.startMonitoring();
  monitor.startTimer('sensorStart');
  
  try {
    await sensorManager.startCollection({
      sampleRate: 50,
      windowSize: 50
    });
    
    const startTime = monitor.endTimer('sensorStart');
    console.log('传感器启动成功');
    console.log('启动时间:', startTime, 'ms');
    
    sensorManager.stopCollection();
    
    return {
      success: true,
      startTime: startTime
    };
  } catch (error) {
    const startTime = monitor.endTimer('sensorStart');
    console.log('传感器启动失败:', error.message);
    console.log('失败时间:', startTime, 'ms');
    
    return {
      success: false,
      startTime: startTime,
      error: error.message
    };
  }
}

/**
 * 测试性能监控器
 */
function testPerformanceMonitor() {
  console.log('\n=== 测试性能监控器 ===');
  
  const monitor = new PerformanceMonitor();
  monitor.startMonitoring();
  
  // 模拟各种操作
  monitor.startTimer('startup');
  setTimeout(() => {
    monitor.endTimer('startup');
    monitor.recordEvent('startup_completed');
  }, 100);
  
  monitor.startTimer('sensorInit');
  setTimeout(() => {
    monitor.endTimer('sensorInit');
    monitor.recordEvent('sensors_initialized');
  }, 200);
  
  // 模拟处理时间
  for (let i = 0; i < 50; i++) {
    monitor.recordProcessingTime(Math.random() * 20 + 5);
  }
  
  // 模拟帧率
  for (let i = 0; i < 30; i++) {
    monitor.recordFrame();
  }
  
  setTimeout(() => {
    const diagnosis = monitor.diagnoseStartupIssues();
    console.log('诊断结果:', diagnosis.summary);
    console.log('发现问题数:', diagnosis.issues.length);
    
    monitor.printReport();
  }, 500);
  
  return monitor;
}

/**
 * 运行完整性能测试
 */
async function runPerformanceTest() {
  console.log('开始PDR系统性能测试...\n');
  
  const results = {
    headingEstimator: null,
    sensorManager: null,
    performanceMonitor: null
  };
  
  try {
    // 测试航向角估计器
    results.headingEstimator = await testHeadingEstimator();
    
    // 测试传感器管理器
    results.sensorManager = await testSensorManager();
    
    // 测试性能监控器
    results.performanceMonitor = testPerformanceMonitor();
    
    // 等待异步操作完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 输出总结
    console.log('\n=== 性能测试总结 ===');
    console.log('航向角估计器:');
    console.log('  - 快速启动:', results.headingEstimator.quickStartSuccess ? '✅' : '❌');
    console.log('  - 平均更新时间:', results.headingEstimator.avgUpdateTime.toFixed(2), 'ms');
    
    console.log('传感器管理器:');
    console.log('  - 启动成功:', results.sensorManager.success ? '✅' : '❌');
    console.log('  - 启动时间:', results.sensorManager.startTime, 'ms');
    
    console.log('性能监控器: ✅ 正常工作');
    
    // 评估优化效果
    const isOptimized = 
      results.headingEstimator.quickStartSuccess &&
      results.headingEstimator.avgUpdateTime < 5 &&
      results.sensorManager.startTime < 2000;
    
    console.log('\n优化效果评估:', isOptimized ? '✅ 优化成功' : '⚠️ 需要进一步优化');
    
    return results;
    
  } catch (error) {
    console.error('性能测试失败:', error);
    return null;
  }
}

/**
 * 压力测试
 */
async function stressTest() {
  console.log('\n=== 压力测试 ===');
  
  const estimator = new HeadingEstimator();
  estimator.quickStart(0);
  
  const startTime = Date.now();
  const iterations = 1000;
  
  for (let i = 0; i < iterations; i++) {
    estimator.updateHeading({
      gyroZ: Math.random() * 0.2 - 0.1,
      compassHeading: Math.random() * 360
    }, Date.now());
  }
  
  const totalTime = Date.now() - startTime;
  const avgTime = totalTime / iterations;
  
  console.log(`${iterations}次更新总时间:`, totalTime, 'ms');
  console.log('平均更新时间:', avgTime.toFixed(3), 'ms');
  console.log('理论最大频率:', (1000 / avgTime).toFixed(1), 'Hz');
  
  return {
    totalTime,
    avgTime,
    maxFrequency: 1000 / avgTime
  };
}

// 如果直接运行此脚本
if (require.main === module) {
  runPerformanceTest()
    .then(() => stressTest())
    .then(() => {
      console.log('\n所有测试完成！');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试失败:', error);
      process.exit(1);
    });
}

module.exports = {
  testHeadingEstimator,
  testSensorManager,
  testPerformanceMonitor,
  runPerformanceTest,
  stressTest
};
