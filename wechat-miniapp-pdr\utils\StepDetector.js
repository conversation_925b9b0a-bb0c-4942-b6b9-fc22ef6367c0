/**
 * 步态检测器
 * 基于原Android项目的StepDetectionNormal.java移植
 */

class StepDetector {
  constructor() {
    // 步态检测状态
    this.stepState = {
      firstPeakFlag: false,
      findStepEndFlag: false,
      curStepStartLocation: 0,
      curStepPeakLocation: 0,
      curStepEndLocation: 0,
      curStepPeakValue: 0,
      preStepPeakLocation: 0,
      preStepPeriod: 1000, // 默认步态周期1000ms
      stepCount: 0,
      curStepStartSqNo: 0,
      curStepPeakSqNo: 0,
      curStepEndSqNo: 0,
      avgStepPeriod: 1000,
      maxAccValue: 0,
      maxAccValueLocation: 0,
      maxAccValueSqNo: 0,
      preStepVar: 0
    };

    // 检测参数
    this.config = {
      accPeakMagnitudeLB: 1.2, // 加速度峰值下限
      accPeakPeriodLB: 300, // 步态周期下限(ms)
      sfAccPeakPeriod: 0.8, // 步态周期比例因子
      slideWindowLen: 21, // 滑动窗口长度
      sampleFreq: 50 // 采样频率Hz
    };

    // 滑动窗口
    this.slideWindow = [];
    
    // 步长估计相关
    this.stepLengthHistory = [];
    this.defaultStepLength = 0.75; // 默认步长(米)
    this.kValue = 0.55606; // 步长估计系数
    
    // 回调函数
    this.onStepDetected = null;
  }

  /**
   * 处理新的加速计数据
   * @param {Object} accData 加速计数据 {x, y, z, magnitude, timestamp}
   */
  processAccelerometerData(accData) {
    // 添加到滑动窗口
    this.slideWindow.push({
      timestamp: accData.timestamp,
      magnitude: accData.magnitude,
      x: accData.x,
      y: accData.y,
      z: accData.z
    });

    // 维持滑动窗口大小
    if (this.slideWindow.length > this.config.slideWindowLen * 2) {
      this.slideWindow.shift();
    }

    // 检查是否有足够数据进行步态检测
    if (this.slideWindow.length < this.config.slideWindowLen) {
      return null;
    }

    // 执行步态检测
    return this.detectStep();
  }

  /**
   * 执行步态检测
   */
  detectStep() {
    const windowLen = this.config.slideWindowLen;
    const midIndex = Math.floor(windowLen / 2);
    
    // 获取当前滑动窗口数据
    const currentWindow = this.slideWindow.slice(-windowLen);
    const accWindow = currentWindow.map(d => d.magnitude);
    const timeWindow = currentWindow.map(d => d.timestamp);
    
    // 当前中点数据
    const midTime = timeWindow[midIndex];
    const midAcc = accWindow[midIndex];
    const midAccPre = accWindow[midIndex - 1] || 0;
    const midAccNext = accWindow[midIndex + 1] || 0;
    
    const currentSqNo = this.slideWindow.length;
    
    let stepDetected = false;
    let stepResult = null;

    // 如果正在寻找步态结束点
    if (this.stepState.findStepEndFlag) {
      // 更新最大加速度值
      if (midAcc > this.stepState.curStepPeakValue && midAcc > this.stepState.maxAccValue) {
        this.stepState.maxAccValue = midAcc;
        this.stepState.maxAccValueLocation = midTime;
        this.stepState.maxAccValueSqNo = currentSqNo;
      }

      const endPointInterval = midTime - this.stepState.curStepPeakLocation;
      
      // 检查是否找到步态结束点
      const quarterPeriod = this.stepState.avgStepPeriod / 4;
      
      if (endPointInterval > quarterPeriod && midAcc <= 0 && midAccNext > 0) {
        // 找到步态结束点
        stepDetected = true;
        this.stepState.curStepEndLocation = midTime;
        this.stepState.curStepEndSqNo = currentSqNo;
        
        const curStepPeriod = this.stepState.curStepEndLocation - this.stepState.curStepStartLocation;
        
        // 更新峰值信息
        if (this.stepState.maxAccValue > this.stepState.curStepPeakValue) {
          this.stepState.curStepPeakValue = this.stepState.maxAccValue;
          this.stepState.curStepPeakLocation = this.stepState.maxAccValueLocation;
          this.stepState.curStepPeakSqNo = this.stepState.maxAccValueSqNo;
        }

        // 创建步态结果
        stepResult = this.createStepResult(curStepPeriod);
        
        // 更新状态为下一步做准备
        this.updateStateAfterStep(curStepPeriod, midTime);
        
      } else if (endPointInterval > this.stepState.avgStepPeriod * 5/4) {
        // 周期太长，强制结束当前步
        stepDetected = true;
        this.stepState.curStepEndLocation = this.stepState.curStepStartLocation + this.stepState.preStepPeriod;
        this.stepState.curStepEndSqNo = -1;
        
        stepResult = this.createStepResult(this.stepState.preStepPeriod);
        this.updateStateAfterStep(this.stepState.preStepPeriod, midTime);
      }
    } else {
      // 寻找步态开始和峰值
      if (midAcc >= midAccPre && midAcc > midAccNext && midAcc >= this.config.accPeakMagnitudeLB) {
        
        if (!this.stepState.firstPeakFlag) {
          // 第一个峰值
          this.stepState.firstPeakFlag = true;
          this.stepState.stepCount++;
          this.stepState.curStepPeakLocation = midTime;
          this.stepState.curStepPeakValue = midAcc;
          this.stepState.curStepPeakSqNo = currentSqNo;
          
          // 寻找步态开始点
          const startIndex = this.findStepStart(accWindow, midIndex);
          this.stepState.curStepStartLocation = timeWindow[startIndex];
          this.stepState.curStepStartSqNo = currentSqNo - (midIndex - startIndex);
          
          this.stepState.findStepEndFlag = true;
          
        } else {
          // 后续峰值，需要检查时间间隔
          const diffPeakTime = midTime - this.stepState.preStepPeakLocation;
          
          if (diffPeakTime >= this.config.accPeakPeriodLB && 
              diffPeakTime > this.config.sfAccPeakPeriod * this.stepState.avgStepPeriod) {
            
            this.stepState.stepCount++;
            this.stepState.curStepPeakLocation = midTime;
            this.stepState.curStepPeakValue = midAcc;
            this.stepState.curStepPeakSqNo = currentSqNo;
            
            const startIndex = this.findStepStart(accWindow, midIndex);
            this.stepState.curStepStartLocation = timeWindow[startIndex];
            this.stepState.curStepStartSqNo = currentSqNo - (midIndex - startIndex);
            
            this.stepState.findStepEndFlag = true;
          }
        }
      }
    }

    return stepDetected ? stepResult : null;
  }

  /**
   * 寻找步态开始点（零交叉点）
   */
  findStepStart(accWindow, midIndex) {
    for (let i = midIndex - 1; i >= 0; i--) {
      if (accWindow[i] === 0) {
        return i;
      }
      if (i < accWindow.length - 1 && accWindow[i + 1] > 0 && accWindow[i] < 0) {
        return i + 1;
      }
    }
    return 0;
  }

  /**
   * 创建步态检测结果
   */
  createStepResult(stepPeriod) {
    const stepResult = {
      startTime: this.stepState.curStepStartLocation,
      peakTime: this.stepState.curStepPeakLocation,
      endTime: this.stepState.curStepEndLocation,
      peakValue: this.stepState.curStepPeakValue,
      period: stepPeriod,
      stepCount: this.stepState.stepCount,
      stepLength: this.estimateStepLength(),
      timestamp: Date.now()
    };

    // 触发回调
    if (this.onStepDetected) {
      this.onStepDetected(stepResult);
    }

    return stepResult;
  }

  /**
   * 步长估计
   * 基于Weinberg模型: SL = K * (a_max - a_min)^0.25
   */
  estimateStepLength() {
    // 获取当前步的加速度数据
    const stepAccData = this.getStepAccelerationData();
    if (!stepAccData || stepAccData.length === 0) {
      return this.defaultStepLength;
    }

    // 计算峰值和谷值
    const maxAcc = Math.max(...stepAccData);
    const minAcc = Math.min(...stepAccData);
    
    // Weinberg模型
    let stepLength = this.kValue * Math.pow(maxAcc - minAcc, 0.25);
    
    // 限制步长范围
    if (Math.abs(stepLength - this.defaultStepLength) > 0.2) {
      stepLength = this.defaultStepLength;
    }
    
    // 更新默认步长
    this.defaultStepLength = stepLength;
    
    // 保存历史数据用于K值训练
    this.stepLengthHistory.push({
      stepLength: stepLength,
      peakValue: maxAcc,
      valleyValue: minAcc
    });
    
    // 限制历史数据长度
    if (this.stepLengthHistory.length > 5) {
      this.stepLengthHistory.shift();
    }

    return stepLength;
  }

  /**
   * 获取当前步的加速度数据
   */
  getStepAccelerationData() {
    const startSqNo = this.stepState.curStepStartSqNo;
    const endSqNo = this.stepState.curStepEndSqNo;
    
    if (startSqNo <= 0 || endSqNo <= 0 || endSqNo <= startSqNo) {
      return null;
    }

    const windowSize = this.slideWindow.length;
    const stepData = [];
    
    for (let i = Math.max(0, windowSize - endSqNo); i < Math.min(windowSize, windowSize - startSqNo); i++) {
      stepData.push(this.slideWindow[i].magnitude);
    }

    return stepData;
  }

  /**
   * 更新步态检测状态
   */
  updateStateAfterStep(stepPeriod, currentTime) {
    this.stepState.findStepEndFlag = false;
    this.stepState.preStepPeriod = stepPeriod;
    this.stepState.avgStepPeriod = (this.stepState.avgStepPeriod + stepPeriod) / 2;
    this.stepState.preStepPeakLocation = this.stepState.curStepPeakLocation;
    
    // 为下一步做准备
    this.stepState.curStepStartLocation = currentTime;
    this.stepState.curStepPeakLocation = 0;
    this.stepState.curStepEndLocation = 0;
    this.stepState.curStepPeakValue = 0;
    this.stepState.curStepStartSqNo = this.stepState.curStepEndSqNo;
    this.stepState.curStepPeakSqNo = 0;
    this.stepState.curStepEndSqNo = 0;
    
    // 重置最大加速度值
    this.stepState.maxAccValue = 0;
    this.stepState.maxAccValueLocation = 0;
    this.stepState.maxAccValueSqNo = 0;
  }

  /**
   * 重置步态检测器
   */
  reset() {
    this.stepState = {
      firstPeakFlag: false,
      findStepEndFlag: false,
      curStepStartLocation: 0,
      curStepPeakLocation: 0,
      curStepEndLocation: 0,
      curStepPeakValue: 0,
      preStepPeakLocation: 0,
      preStepPeriod: 1000,
      stepCount: 0,
      curStepStartSqNo: 0,
      curStepPeakSqNo: 0,
      curStepEndSqNo: 0,
      avgStepPeriod: 1000,
      maxAccValue: 0,
      maxAccValueLocation: 0,
      maxAccValueSqNo: 0,
      preStepVar: 0
    };
    
    this.slideWindow = [];
    this.stepLengthHistory = [];
  }

  /**
   * 设置检测参数
   */
  setConfig(config) {
    this.config = { ...this.config, ...config };
  }

  /**
   * 设置步态检测回调
   */
  setStepDetectedCallback(callback) {
    this.onStepDetected = callback;
  }

  /**
   * 获取当前步数
   */
  getStepCount() {
    return this.stepState.stepCount;
  }

  /**
   * 获取平均步长
   */
  getAverageStepLength() {
    if (this.stepLengthHistory.length === 0) {
      return this.defaultStepLength;
    }
    
    const totalLength = this.stepLengthHistory.reduce((sum, item) => sum + item.stepLength, 0);
    return totalLength / this.stepLengthHistory.length;
  }

  /**
   * 设置默认步长
   */
  setDefaultStepLength(stepLength) {
    this.defaultStepLength = stepLength;
  }

  /**
   * 获取步态检测状态信息
   */
  getStateInfo() {
    return {
      stepCount: this.stepState.stepCount,
      avgStepPeriod: this.stepState.avgStepPeriod,
      avgStepLength: this.getAverageStepLength(),
      isDetecting: this.stepState.findStepEndFlag,
      bufferSize: this.slideWindow.length
    };
  }
}

export default StepDetector;