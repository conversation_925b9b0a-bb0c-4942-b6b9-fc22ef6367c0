/**
 * PDR定位引擎 - 集成所有算法模块
 * Pedestrian Dead Reckoning Engine
 */

import SensorManager from './SensorManager.js';
import StepDetector from './StepDetector.js';
import HeadingEstimator from './HeadingEstimator.js';
import MLAMatcher from './MLAMatcher.js';

class PDREngine {
  constructor() {
    // 算法模块
    this.sensorManager = new SensorManager();
    this.stepDetector = new StepDetector();
    this.headingEstimator = new HeadingEstimator();
    this.mlaMatcher = new MLAMatcher();
    
    // 定位状态
    this.isRunning = false;
    this.isPaused = false;
    
    // 当前状态
    this.currentState = {
      position: { x: 0, y: 0, z: 0 },
      heading: 0,
      velocity: 0,
      stepCount: 0,
      floor: 4,
      timestamp: 0,
      confidence: 0 // 定位置信度
    };
    
    // 轨迹历史
    this.trajectory = [];
    this.maxTrajectoryPoints = 1000;
    
    // 融合参数
    this.fusionParams = {
      pdrWeight: 0.7,        // PDR权重
      mlaWeight: 0.3,        // MLA权重
      adaptiveWeighting: true, // 自适应权重
      confidenceThreshold: 0.6, // 置信度阈值
      outlierThreshold: 3.0,   // 异常值阈值(米)
      smoothingFactor: 0.8     // 平滑因子
    };
    
    // 性能统计
    this.statistics = {
      totalSteps: 0,
      totalDistance: 0,
      averageStepLength: 0.75,
      processingTime: [],
      mlaMatches: 0,
      corrections: 0
    };
    
    // 回调函数
    this.callbacks = {
      onPositionUpdate: null,
      onStepDetected: null,
      onHeadingUpdate: null,
      onMlaMatched: null,
      onError: null
    };
    
    this.initializeModules();
  }

  /**
   * 初始化各个模块
   */
  initializeModules() {
    // 设置传感器管理器回调
    this.sensorManager.setCallbacks({
      onAccelerometerData: this.onAccelerometerData.bind(this),
      onGyroscopeData: this.onGyroscopeData.bind(this),
      onCompassData: this.onCompassData.bind(this),
      onError: this.onSensorError.bind(this)
    });

    // 设置步态检测器回调
    this.stepDetector.setStepDetectedCallback(this.onStepDetected.bind(this));

    // 初始化MLA匹配器
    this.mlaMatcher.initializeMlaDatabase();
    this.mlaMatcher.setCallbacks({
      onNodeMatched: this.onMlaNodeMatched.bind(this),
      onPositionCorrected: this.onPositionCorrected.bind(this)
    });

    console.log('PDR引擎模块初始化完成');
  }

  /**
   * 启动定位引擎
   */
  async start(config = {}) {
    if (this.isRunning) {
      console.warn('PDR引擎已在运行中');
      return;
    }

    try {
      console.log('启动PDR定位引擎...');
      
      // 应用配置
      this.applyConfig(config);
      
      // 启动传感器数据收集
      this.sensorManager.startCollection({
        sampleRate: config.sampleRate || 50,
        windowSize: config.windowSize || 50
      });

      // 重置状态
      this.resetState();
      
      this.isRunning = true;
      this.isPaused = false;
      
      console.log('PDR定位引擎启动成功');
      
      return true;
    } catch (error) {
      console.error('PDR引擎启动失败:', error);
      if (this.callbacks.onError) {
        this.callbacks.onError('engine_start', error);
      }
      throw error;
    }
  }

  /**
   * 停止定位引擎
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    console.log('停止PDR定位引擎...');
    
    // 停止传感器数据收集
    this.sensorManager.stopCollection();
    
    this.isRunning = false;
    this.isPaused = false;
    
    console.log('PDR定位引擎已停止');
  }

  /**
   * 暂停定位引擎
   */
  pause() {
    if (!this.isRunning || this.isPaused) {
      return;
    }

    this.isPaused = true;
    console.log('PDR定位引擎已暂停');
  }

  /**
   * 恢复定位引擎
   */
  resume() {
    if (!this.isRunning || !this.isPaused) {
      return;
    }

    this.isPaused = false;
    console.log('PDR定位引擎已恢复');
  }

  /**
   * 重置定位状态
   */
  resetState() {
    // 重置算法模块
    this.stepDetector.reset();
    this.headingEstimator.reset();
    this.mlaMatcher.reset();
    
    // 重置状态
    this.currentState = {
      position: { x: 0, y: 0, z: 0 },
      heading: 0,
      velocity: 0,
      stepCount: 0,
      floor: 4,
      timestamp: Date.now(),
      confidence: 1.0
    };
    
    // 清空轨迹
    this.trajectory = [];
    
    // 重置统计
    this.statistics = {
      totalSteps: 0,
      totalDistance: 0,
      averageStepLength: 0.75,
      processingTime: [],
      mlaMatches: 0,
      corrections: 0
    };
    
    console.log('PDR引擎状态已重置');
  }

  /**
   * 应用配置参数
   */
  applyConfig(config) {
    if (config.fusionParams) {
      this.fusionParams = { ...this.fusionParams, ...config.fusionParams };
    }
    
    if (config.initialPosition) {
      this.currentState.position = { ...config.initialPosition };
      this.mlaMatcher.setInitialPosition(config.initialPosition);
    }
    
    if (config.initialFloor) {
      this.currentState.floor = config.initialFloor;
      this.mlaMatcher.setCurrentFloor(config.initialFloor);
    }
    
    if (config.stepDetectorParams) {
      this.stepDetector.setConfig(config.stepDetectorParams);
    }
    
    if (config.headingEstimatorParams) {
      this.headingEstimator.setConfig(config.headingEstimatorParams);
    }
    
    if (config.mlaMatcherParams) {
      this.mlaMatcher.setConfig(config.mlaMatcherParams);
    }
  }

  /**
   * 传感器数据处理
   */
  onAccelerometerData(data) {
    if (!this.isRunning || this.isPaused) return;
    
    const startTime = Date.now();
    
    // 步态检测
    const stepResult = this.stepDetector.processAccelerometerData(data);
    
    // 传递给MLA匹配器用于加速计节点匹配
    this.processMLAMatching({
      accelerometer: [data],
      timestamp: data.timestamp
    });
    
    // 记录处理时间
    this.recordProcessingTime(Date.now() - startTime);
  }

  onGyroscopeData(data) {
    if (!this.isRunning || this.isPaused) return;
    
    // 航向角估计
    const heading = this.headingEstimator.updateHeading({
      gyroZ: data.z
    }, data.timestamp);
    
    // 更新当前航向
    this.updateHeading(heading);
    
    // 传递给MLA匹配器用于陀螺仪节点匹配
    this.processMLAMatching({
      gyroscope: data,
      timestamp: data.timestamp
    });
  }

  onCompassData(data) {
    if (!this.isRunning || this.isPaused) return;
    
    // 航向角估计
    const heading = this.headingEstimator.updateHeading({
      compassHeading: data.direction
    }, data.timestamp);
    
    // 更新当前航向
    this.updateHeading(heading);
  }

  onSensorError(sensorType, error) {
    console.error(`传感器错误 [${sensorType}]:`, error);
    if (this.callbacks.onError) {
      this.callbacks.onError(sensorType, error);
    }
  }

  /**
   * 步态检测回调
   */
  onStepDetected(stepResult) {
    if (!this.isRunning || this.isPaused) return;
    
    console.log('检测到步态:', stepResult);
    
    // 更新统计信息
    this.statistics.totalSteps++;
    this.statistics.averageStepLength = 
      (this.statistics.averageStepLength * (this.statistics.totalSteps - 1) + stepResult.stepLength) 
      / this.statistics.totalSteps;
    
    // 计算速度
    this.updateVelocity(stepResult);
    
    // 更新位置 (PDR递推)
    this.updatePositionPDR(stepResult);
    
    // 触发回调
    if (this.callbacks.onStepDetected) {
      this.callbacks.onStepDetected(stepResult);
    }
  }

  /**
   * 更新航向角
   */
  updateHeading(heading) {
    // 平滑处理
    const smoothedHeading = this.smoothHeading(this.currentState.heading, heading);
    
    this.currentState.heading = smoothedHeading;
    this.currentState.timestamp = Date.now();
    
    // 触发回调
    if (this.callbacks.onHeadingUpdate) {
      this.callbacks.onHeadingUpdate(smoothedHeading);
    }
  }

  /**
   * 航向角平滑处理
   */
  smoothHeading(currentHeading, newHeading) {
    const factor = this.fusionParams.smoothingFactor;
    
    // 处理角度跨越问题
    let diff = newHeading - currentHeading;
    if (diff > 180) diff -= 360;
    if (diff < -180) diff += 360;
    
    return this.normalizeAngle(currentHeading + factor * diff);
  }

  /**
   * 计算速度
   */
  updateVelocity(stepResult) {
    const currentTime = stepResult.timestamp;
    const stepPeriod = stepResult.period || 1000; // 默认1秒
    
    this.currentState.velocity = (stepResult.stepLength * 1000) / stepPeriod; // m/s
  }

  /**
   * PDR位置更新
   */
  updatePositionPDR(stepResult) {
    const currentPos = this.currentState.position;
    const heading = this.currentState.heading * Math.PI / 180; // 转弧度
    const stepLength = stepResult.stepLength;
    
    // 计算新位置
    const newPosition = {
      x: currentPos.x + stepLength * Math.cos(heading),
      y: currentPos.y + stepLength * Math.sin(heading),
      z: currentPos.z // Z坐标通过MLA匹配或其他方式更新
    };
    
    // 更新统计
    this.statistics.totalDistance += stepLength;
    
    // 异常检测
    const displacement = Math.sqrt(
      Math.pow(newPosition.x - currentPos.x, 2) + 
      Math.pow(newPosition.y - currentPos.y, 2)
    );
    
    if (displacement > this.fusionParams.outlierThreshold) {
      console.warn('检测到异常位移:', displacement);
      // 可以选择忽略或降低权重
    }
    
    // 更新位置
    this.currentState.position = newPosition;
    this.currentState.stepCount = stepResult.stepCount;
    this.currentState.timestamp = stepResult.timestamp;
    
    // 添加到轨迹
    this.addToTrajectory(newPosition, stepResult.timestamp);
    
    // 触发位置更新回调
    this.triggerPositionUpdate();
  }

  /**
   * MLA匹配处理
   */
  processMLAMatching(sensorData) {
    const matchResult = this.mlaMatcher.matchNodes(sensorData, this.currentState.position);
    
    if (matchResult) {
      this.statistics.mlaMatches++;
    }
  }

  /**
   * MLA节点匹配回调
   */
  onMlaNodeMatched(match) {
    console.log('MLA节点匹配:', match);
    
    // 更新置信度
    this.updateConfidence(match.confidence);
    
    // 楼层更新
    if (match.type === 'pressure' && match.floorChange) {
      this.currentState.floor = match.floorChange.to;
    }
    
    if (this.callbacks.onMlaMatched) {
      this.callbacks.onMlaMatched(match);
    }
  }

  /**
   * 位置校正回调
   */
  onPositionCorrected(correctedPosition) {
    console.log('位置校正:', correctedPosition);
    
    // 计算校正距离
    const correctionDistance = Math.sqrt(
      Math.pow(correctedPosition.x - this.currentState.position.x, 2) + 
      Math.pow(correctedPosition.y - this.currentState.position.y, 2) + 
      Math.pow(correctedPosition.z - this.currentState.position.z, 2)
    );
    
    console.log('校正距离:', correctionDistance);
    
    // 自适应权重调整
    if (this.fusionParams.adaptiveWeighting) {
      this.adjustFusionWeights(correctionDistance);
    }
    
    // 融合位置
    const fusedPosition = this.fusePositions(this.currentState.position, correctedPosition);
    
    this.currentState.position = fusedPosition;
    this.currentState.timestamp = Date.now();
    this.statistics.corrections++;
    
    // 更新轨迹最后一个点
    if (this.trajectory.length > 0) {
      this.trajectory[this.trajectory.length - 1] = {
        ...fusedPosition,
        timestamp: this.currentState.timestamp,
        corrected: true
      };
    }
    
    // 触发位置更新回调
    this.triggerPositionUpdate();
  }

  /**
   * 位置融合
   */
  fusePositions(pdrPos, mlaPos) {
    const pdrWeight = this.fusionParams.pdrWeight;
    const mlaWeight = this.fusionParams.mlaWeight;
    
    // 归一化权重
    const totalWeight = pdrWeight + mlaWeight;
    const normalizedPdrWeight = pdrWeight / totalWeight;
    const normalizedMlaWeight = mlaWeight / totalWeight;
    
    return {
      x: pdrPos.x * normalizedPdrWeight + mlaPos.x * normalizedMlaWeight,
      y: pdrPos.y * normalizedPdrWeight + mlaPos.y * normalizedMlaWeight,
      z: pdrPos.z * normalizedPdrWeight + mlaPos.z * normalizedMlaWeight
    };
  }

  /**
   * 自适应权重调整
   */
  adjustFusionWeights(correctionDistance) {
    // 根据校正距离动态调整权重
    if (correctionDistance > 2.0) {
      // 校正距离大，提高MLA权重
      this.fusionParams.mlaWeight = Math.min(0.7, this.fusionParams.mlaWeight + 0.1);
      this.fusionParams.pdrWeight = 1.0 - this.fusionParams.mlaWeight;
    } else if (correctionDistance < 0.5) {
      // 校正距离小，提高PDR权重
      this.fusionParams.pdrWeight = Math.min(0.8, this.fusionParams.pdrWeight + 0.1);
      this.fusionParams.mlaWeight = 1.0 - this.fusionParams.pdrWeight;
    }
    
    console.log('权重调整:', {
      pdrWeight: this.fusionParams.pdrWeight,
      mlaWeight: this.fusionParams.mlaWeight,
      correctionDistance: correctionDistance
    });
  }

  /**
   * 更新置信度
   */
  updateConfidence(matchConfidence) {
    // 基于MLA匹配置信度和PDR连续性更新整体置信度
    const continuityFactor = Math.min(1.0, this.statistics.totalSteps / 10.0);
    this.currentState.confidence = (matchConfidence * 0.6 + continuityFactor * 0.4);
  }

  /**
   * 添加到轨迹
   */
  addToTrajectory(position, timestamp) {
    const trajectoryPoint = {
      ...position,
      timestamp: timestamp,
      stepCount: this.currentState.stepCount,
      heading: this.currentState.heading,
      confidence: this.currentState.confidence
    };
    
    this.trajectory.push(trajectoryPoint);
    
    // 限制轨迹点数量
    if (this.trajectory.length > this.maxTrajectoryPoints) {
      this.trajectory.shift();
    }
  }

  /**
   * 触发位置更新回调
   */
  triggerPositionUpdate() {
    if (this.callbacks.onPositionUpdate) {
      this.callbacks.onPositionUpdate({
        position: { ...this.currentState.position },
        heading: this.currentState.heading,
        velocity: this.currentState.velocity,
        stepCount: this.currentState.stepCount,
        floor: this.currentState.floor,
        confidence: this.currentState.confidence,
        timestamp: this.currentState.timestamp
      });
    }
  }

  /**
   * 记录处理时间
   */
  recordProcessingTime(time) {
    this.statistics.processingTime.push(time);
    
    // 保持最近100个处理时间记录
    if (this.statistics.processingTime.length > 100) {
      this.statistics.processingTime.shift();
    }
  }

  /**
   * 角度归一化
   */
  normalizeAngle(angle) {
    while (angle > 180) angle -= 360;
    while (angle < -180) angle += 360;
    return angle;
  }

  /**
   * 获取当前状态
   */
  getCurrentState() {
    return { ...this.currentState };
  }

  /**
   * 获取轨迹
   */
  getTrajectory() {
    return [...this.trajectory];
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const avgProcessingTime = this.statistics.processingTime.length > 0 
      ? this.statistics.processingTime.reduce((sum, time) => sum + time, 0) / this.statistics.processingTime.length
      : 0;
    
    return {
      ...this.statistics,
      averageProcessingTime: avgProcessingTime,
      matchRate: this.statistics.totalSteps > 0 ? this.statistics.mlaMatches / this.statistics.totalSteps : 0,
      correctionRate: this.statistics.totalSteps > 0 ? this.statistics.corrections / this.statistics.totalSteps : 0
    };
  }

  /**
   * 设置回调函数
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 设置MLA数据库
   */
  setMlaDatabase(mlaData) {
    this.mlaMatcher.initializeMlaDatabase(mlaData);
  }

  /**
   * 校准传感器
   */
  async calibrateSensors(duration = 3000) {
    if (this.isRunning) {
      throw new Error('请先停止定位再进行校准');
    }
    
    // 临时启动传感器进行校准
    this.sensorManager.startCollection();
    
    try {
      const result = await this.sensorManager.calibrateSensors(duration);
      
      // 将校准结果应用到航向估计器
      this.headingEstimator.reset();
      
      return result;
    } finally {
      this.sensorManager.stopCollection();
    }
  }

  /**
   * 导出数据
   */
  exportData() {
    return {
      trajectory: this.getTrajectory(),
      statistics: this.getStatistics(),
      currentState: this.getCurrentState(),
      fusionParams: this.fusionParams,
      timestamp: Date.now()
    };
  }

  /**
   * 获取性能信息
   */
  getPerformanceInfo() {
    const stats = this.getStatistics();
    return {
      averageProcessingTime: stats.averageProcessingTime,
      totalSteps: stats.totalSteps,
      totalDistance: stats.totalDistance,
      matchRate: stats.matchRate,
      correctionRate: stats.correctionRate,
      confidence: this.currentState.confidence,
      isRunning: this.isRunning,
      memoryUsage: this.trajectory.length
    };
  }
}

export default PDREngine;