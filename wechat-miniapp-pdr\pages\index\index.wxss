/* 室内惯导定位首页样式 */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 头部状态栏 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.status-label {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.status-value {
  font-size: 32rpx;
  font-weight: bold;
}

.status-value.active {
  color: #4CAF50;
  text-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);
}

/* 画布容器 */
.canvas-container {
  position: relative;
  flex: 1;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: white;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.tracking-canvas {
  width: 100%;
  height: 100%;
  background: #fafafa;
}

/* 画布控制按钮 */
.canvas-controls {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.control-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);
  transition: all 0.3s ease;
}

.control-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 1);
}

.iconfont {
  font-size: 32rpx;
  color: #666;
}

/* 信息面板 */
.info-panel {
  background: white;
  margin: 0 20rpx 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
}

.expand-icon {
  font-size: 24rpx;
  transition: transform 0.3s ease;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

.panel-content {
  padding: 30rpx;
  max-height: 800rpx;
  overflow-y: auto;
}

.info-section {
  margin-bottom: 40rpx;
}

.info-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #eee;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 160rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
  flex: 1;
}

/* 控制按钮 */
.controls {
  padding: 20rpx;
}

.control-button {
  width: 100%;
  height: 100rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  border: none;
  transition: all 0.3s ease;
}

.control-button.start {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
}

.control-button.stop {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(244, 67, 54, 0.3);
}

.control-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.secondary-button {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  background: white;
  color: #666;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.secondary-button:active {
  background: #f5f5f5;
  transform: scale(0.98);
}

/* 加载指示器 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 40rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: white;
  font-size: 28rpx;
}

/* 提示信息 */
.toast {
  position: fixed;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1001;
}

.toast.show {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .status-bar {
    padding: 0 10rpx;
  }
  
  .status-value {
    font-size: 28rpx;
  }
  
  .canvas-controls {
    top: 10rpx;
    right: 10rpx;
  }
  
  .control-btn {
    width: 60rpx;
    height: 60rpx;
  }
  
  .iconfont {
    font-size: 24rpx;
  }
}

/* 动画效果 */
.info-panel.expanded {
  animation: expandPanel 0.3s ease-out;
}

@keyframes expandPanel {
  from {
    max-height: 120rpx;
  }
  to {
    max-height: 1000rpx;
  }
}

/* 轨迹点样式（通过canvas绘制，这里是参考） */
.track-point {
  width: 8rpx;
  height: 8rpx;
  background: #2196F3;
  border-radius: 50%;
  position: absolute;
}

.current-position {
  width: 16rpx;
  height: 16rpx;
  background: #4CAF50;
  border: 4rpx solid rgba(76, 175, 80, 0.3);
  border-radius: 50%;
  position: absolute;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 20rpx rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

/* MLA节点样式 */
.mla-node {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  position: absolute;
}

.mla-node.gyro {
  background: #FF9800;
  border: 2rpx solid rgba(255, 152, 0, 0.3);
}

.mla-node.pressure {
  background: #9C27B0;
  border: 2rpx solid rgba(156, 39, 176, 0.3);
}

.mla-node.accelerometer {
  background: #F44336;
  border: 2rpx solid rgba(244, 67, 54, 0.3);
}

.mla-node.matched {
  animation: matchedPulse 1s ease-out;
}

@keyframes matchedPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  50% {
    transform: scale(1.5);
    box-shadow: 0 0 0 15rpx rgba(76, 175, 80, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 30rpx rgba(76, 175, 80, 0);
  }
}