# 微信小程序 async/await 错误修复

## 问题描述

错误信息：
```
Error: file: pages/tracking/tracking.js
unknown: Unexpected reserved word 'await'. (473:33)

471 |           // 保存到本地存储 - 使用安全的文件系统
472 |           try {
> 473 |             const existingData = await safeFileSystem.safeGetStorage('trajectoryHistory', []);
    |                                  ^
```

## 问题原因

在微信小程序中，普通的回调函数（如 `success` 回调）不能直接使用 `await` 关键字，因为它们不是 `async` 函数。这是JavaScript语法限制。

### 错误的用法：
```javascript
wx.showModal({
  success: (res) => {  // 这不是async函数
    const data = await someAsyncFunction(); // ❌ 错误！
  }
});
```

### 正确的用法：
```javascript
// 方法1：将回调函数声明为async
wx.showModal({
  success: async (res) => {  // ✅ 正确
    const data = await someAsyncFunction();
  }
});

// 方法2：使用同步方法
wx.showModal({
  success: (res) => {
    const data = someSyncFunction(); // ✅ 正确
  }
});

// 方法3：将异步逻辑提取到单独的async方法中
wx.showModal({
  success: (res) => {
    this.handleModalSuccess(res); // ✅ 正确
  }
});

async handleModalSuccess(res) {
  const data = await someAsyncFunction();
}
```

## 解决方案

### 1. 修改SafeFileSystem.js

添加了同步版本的存储方法：

```javascript
// 同步版本 - 用于普通回调函数
safeSetStorage(key, data) {
  try {
    if (typeof wx !== 'undefined' && wx.setStorageSync) {
      wx.setStorageSync(key, data);
      return true;
    }
    return false;
  } catch (error) {
    console.warn('存储数据失败:', error.message);
    return false;
  }
}

safeGetStorage(key, defaultValue = null) {
  try {
    if (typeof wx !== 'undefined' && wx.getStorageSync) {
      const data = wx.getStorageSync(key);
      return data !== '' ? data : defaultValue;
    }
    return defaultValue;
  } catch (error) {
    console.warn('获取存储数据失败:', error.message);
    return defaultValue;
  }
}

// 异步版本 - 用于async函数
async safeSetStorageAsync(key, data) {
  return new Promise((resolve) => {
    try {
      if (typeof wx !== 'undefined' && wx.setStorage) {
        wx.setStorage({
          key: key,
          data: data,
          success: () => resolve(true),
          fail: (error) => {
            console.warn('异步存储数据失败:', error.errMsg);
            resolve(false);
          }
        });
      } else {
        resolve(this.safeSetStorage(key, data));
      }
    } catch (error) {
      console.warn('异步存储数据异常:', error.message);
      resolve(false);
    }
  });
}
```

### 2. 修改tracking.js

#### 原来的错误代码：
```javascript
success: (res) => {
  // ... 其他代码
  const existingData = await safeFileSystem.safeGetStorage('trajectoryHistory', []);
  // ❌ 错误：在非async回调中使用await
}
```

#### 修复后的代码：
```javascript
success: (res) => {
  // ... 其他代码
  this.saveTrajectoryRecord(trajectoryRecord); // ✅ 正确：调用单独的方法
}

// 新增的方法
saveTrajectoryRecord(trajectoryRecord) {
  try {
    // 使用同步版本的安全存储
    if (typeof safeFileSystem !== 'undefined') {
      const existingData = safeFileSystem.safeGetStorage('trajectoryHistory', []);
      existingData.unshift(trajectoryRecord);

      if (existingData.length > 50) {
        existingData.splice(50);
      }

      const saveSuccess = safeFileSystem.safeSetStorage('trajectoryHistory', existingData);

      if (saveSuccess) {
        this.showToast('轨迹保存成功');
        this.loadTrajectoryData();
      } else {
        this.showToast('保存失败，请重试');
      }
    } else {
      // 降级到原生微信存储API
      const existingData = wx.getStorageSync('trajectoryHistory') || [];
      existingData.unshift(trajectoryRecord);

      if (existingData.length > 50) {
        existingData.splice(50);
      }

      wx.setStorageSync('trajectoryHistory', existingData);
      this.showToast('轨迹保存成功');
      this.loadTrajectoryData();
    }
  } catch (error) {
    console.error('保存轨迹失败:', error);
    this.showToast('保存失败');
  }
}
```

### 3. 更新loadTrajectoryData方法

```javascript
loadTrajectoryData() {
  this.setData({ isLoading: true, loadingText: '加载轨迹数据...' });
  
  try {
    // 使用安全的文件系统加载数据
    let trajectoryData = [];
    if (typeof safeFileSystem !== 'undefined') {
      trajectoryData = safeFileSystem.safeGetStorage('trajectoryHistory', []);
    } else {
      trajectoryData = wx.getStorageSync('trajectoryHistory') || [];
    }
    
    // ... 其他处理逻辑
  } catch (error) {
    console.error('加载轨迹数据失败:', error);
    this.showToast('加载轨迹数据失败');
  } finally {
    this.setData({ isLoading: false });
  }
}
```

## 最佳实践

### 1. 微信小程序中的异步处理

```javascript
// ✅ 正确：在Page方法中使用async/await
Page({
  async onLoad() {
    const data = await this.loadData();
  },
  
  async loadData() {
    // 可以使用await
    return await someAsyncFunction();
  },
  
  // ✅ 正确：事件处理函数可以是async
  async onButtonTap() {
    const result = await this.processData();
  }
});

// ❌ 错误：在微信API回调中直接使用await
wx.request({
  success: (res) => {
    const data = await processResponse(res); // ❌ 错误
  }
});

// ✅ 正确：使用async回调或提取方法
wx.request({
  success: async (res) => {  // 方法1：async回调
    const data = await processResponse(res);
  }
});

// 或者
wx.request({
  success: (res) => {  // 方法2：提取方法
    this.handleResponse(res);
  }
});

async handleResponse(res) {
  const data = await processResponse(res);
}
```

### 2. 存储操作的选择

```javascript
// 同步操作 - 适用于简单场景
const data = wx.getStorageSync('key');
wx.setStorageSync('key', data);

// 异步操作 - 适用于大数据或需要错误处理的场景
wx.getStorage({
  key: 'key',
  success: (res) => {
    console.log(res.data);
  },
  fail: (error) => {
    console.error(error);
  }
});

// Promise包装 - 适用于async/await场景
function getStorageAsync(key) {
  return new Promise((resolve, reject) => {
    wx.getStorage({
      key: key,
      success: (res) => resolve(res.data),
      fail: (error) => reject(error)
    });
  });
}

async function loadData() {
  try {
    const data = await getStorageAsync('key');
    return data;
  } catch (error) {
    console.error('加载失败:', error);
    return null;
  }
}
```

### 3. 错误处理策略

```javascript
// 降级策略：优先使用安全包装器，失败时降级到原生API
function safeGetStorage(key, defaultValue = null) {
  try {
    if (typeof safeFileSystem !== 'undefined') {
      return safeFileSystem.safeGetStorage(key, defaultValue);
    } else {
      return wx.getStorageSync(key) || defaultValue;
    }
  } catch (error) {
    console.warn('存储操作失败:', error);
    return defaultValue;
  }
}
```

## 验证修复

### 1. 编译检查
确保代码能够正常编译，没有语法错误：
```bash
# 在微信开发者工具中查看编译结果
# 应该没有 "Unexpected reserved word 'await'" 错误
```

### 2. 功能测试
1. 测试轨迹保存功能
2. 测试轨迹加载功能
3. 测试错误处理机制

### 3. 性能测试
- 同步操作应该更快
- 不会阻塞UI线程
- 错误处理不影响用户体验

## 总结

通过以下修改解决了async/await错误：

1. **SafeFileSystem.js**：
   - ✅ 添加同步版本的存储方法
   - ✅ 保留异步版本用于特殊场景
   - ✅ 提供降级策略

2. **tracking.js**：
   - ✅ 将异步逻辑提取到单独的方法
   - ✅ 在回调函数中调用同步方法
   - ✅ 保持错误处理机制

3. **最佳实践**：
   - ✅ 区分同步和异步场景
   - ✅ 正确使用async/await
   - ✅ 提供完善的错误处理

修复后的代码：
- 🚀 编译正常，无语法错误
- 🛡️ 保持错误处理能力
- ⚡ 性能更好（同步操作）
- 🔄 向后兼容，有降级策略
