/**
 * 安全的文件系统包装器
 * 用于处理微信小程序中的文件系统访问错误
 */

class SafeFileSystem {
  constructor() {
    this.isAvailable = false;
    this.fileSystemManager = null;
    this.initializeFileSystem();
  }

  /**
   * 初始化文件系统
   */
  initializeFileSystem() {
    try {
      if (typeof wx !== 'undefined' && wx.getFileSystemManager) {
        this.fileSystemManager = wx.getFileSystemManager();
        this.isAvailable = true;
        console.log('文件系统管理器初始化成功');
      } else {
        console.warn('文件系统管理器不可用');
      }
    } catch (error) {
      console.warn('文件系统管理器初始化失败:', error.message);
      this.isAvailable = false;
    }
  }

  /**
   * 安全的文件读取
   */
  async readFile(filePath, encoding = 'utf8') {
    if (!this.isAvailable) {
      throw new Error('文件系统不可用');
    }

    return new Promise((resolve, reject) => {
      try {
        this.fileSystemManager.readFile({
          filePath: filePath,
          encoding: encoding,
          success: (res) => {
            resolve(res.data);
          },
          fail: (error) => {
            // 静默处理特定的文件系统错误
            if (this.isIgnorableError(error)) {
              console.warn('文件读取失败（已忽略）:', error.errMsg);
              resolve(null);
            } else {
              reject(new Error(`文件读取失败: ${error.errMsg}`));
            }
          }
        });
      } catch (error) {
        reject(new Error(`文件读取异常: ${error.message}`));
      }
    });
  }

  /**
   * 安全的文件写入
   */
  async writeFile(filePath, data, encoding = 'utf8') {
    if (!this.isAvailable) {
      throw new Error('文件系统不可用');
    }

    return new Promise((resolve, reject) => {
      try {
        this.fileSystemManager.writeFile({
          filePath: filePath,
          data: data,
          encoding: encoding,
          success: () => {
            resolve(true);
          },
          fail: (error) => {
            // 静默处理特定的文件系统错误
            if (this.isIgnorableError(error)) {
              console.warn('文件写入失败（已忽略）:', error.errMsg);
              resolve(false);
            } else {
              reject(new Error(`文件写入失败: ${error.errMsg}`));
            }
          }
        });
      } catch (error) {
        reject(new Error(`文件写入异常: ${error.message}`));
      }
    });
  }

  /**
   * 安全的目录创建
   */
  async mkdir(dirPath, recursive = false) {
    if (!this.isAvailable) {
      throw new Error('文件系统不可用');
    }

    return new Promise((resolve, reject) => {
      try {
        this.fileSystemManager.mkdir({
          dirPath: dirPath,
          recursive: recursive,
          success: () => {
            resolve(true);
          },
          fail: (error) => {
            // 静默处理特定的文件系统错误
            if (this.isIgnorableError(error) || error.errMsg.includes('file already exists')) {
              console.warn('目录创建失败（已忽略）:', error.errMsg);
              resolve(false);
            } else {
              reject(new Error(`目录创建失败: ${error.errMsg}`));
            }
          }
        });
      } catch (error) {
        reject(new Error(`目录创建异常: ${error.message}`));
      }
    });
  }

  /**
   * 安全的文件/目录访问检查
   */
  async access(path) {
    if (!this.isAvailable) {
      return false;
    }

    return new Promise((resolve) => {
      try {
        this.fileSystemManager.access({
          path: path,
          success: () => {
            resolve(true);
          },
          fail: (error) => {
            // 所有访问失败都静默处理
            resolve(false);
          }
        });
      } catch (error) {
        resolve(false);
      }
    });
  }

  /**
   * 安全的目录读取
   */
  async readdir(dirPath) {
    if (!this.isAvailable) {
      throw new Error('文件系统不可用');
    }

    return new Promise((resolve, reject) => {
      try {
        this.fileSystemManager.readdir({
          dirPath: dirPath,
          success: (res) => {
            resolve(res.files);
          },
          fail: (error) => {
            // 静默处理特定的文件系统错误
            if (this.isIgnorableError(error)) {
              console.warn('目录读取失败（已忽略）:', error.errMsg);
              resolve([]);
            } else {
              reject(new Error(`目录读取失败: ${error.errMsg}`));
            }
          }
        });
      } catch (error) {
        reject(new Error(`目录读取异常: ${error.message}`));
      }
    });
  }

  /**
   * 检查是否是可忽略的错误
   */
  isIgnorableError(error) {
    if (!error || !error.errMsg) return false;
    
    const ignorablePatterns = [
      'no such file or directory',
      'wxfile://',
      'miniprogramLog',
      'permission denied',
      'access denied',
      'operation not permitted'
    ];
    
    const errorMsg = error.errMsg.toLowerCase();
    return ignorablePatterns.some(pattern => 
      errorMsg.includes(pattern.toLowerCase())
    );
  }

  /**
   * 获取用户文档目录路径
   */
  getUserDocumentsPath() {
    if (typeof wx !== 'undefined' && wx.env) {
      return `${wx.env.USER_DATA_PATH}/documents`;
    }
    return null;
  }

  /**
   * 获取临时文件目录路径
   */
  getTempPath() {
    if (typeof wx !== 'undefined' && wx.env) {
      return wx.env.USER_DATA_PATH;
    }
    return null;
  }

  /**
   * 安全的存储数据到本地 - 同步版本
   */
  safeSetStorage(key, data) {
    try {
      if (typeof wx !== 'undefined' && wx.setStorageSync) {
        wx.setStorageSync(key, data);
        return true;
      }
      return false;
    } catch (error) {
      console.warn('存储数据失败:', error.message);
      return false;
    }
  }

  /**
   * 安全的从本地获取数据 - 同步版本
   */
  safeGetStorage(key, defaultValue = null) {
    try {
      if (typeof wx !== 'undefined' && wx.getStorageSync) {
        const data = wx.getStorageSync(key);
        return data !== '' ? data : defaultValue;
      }
      return defaultValue;
    } catch (error) {
      console.warn('获取存储数据失败:', error.message);
      return defaultValue;
    }
  }

  /**
   * 安全的清除存储数据 - 同步版本
   */
  safeClearStorage(key) {
    try {
      if (typeof wx !== 'undefined' && wx.removeStorageSync) {
        wx.removeStorageSync(key);
        return true;
      }
      return false;
    } catch (error) {
      console.warn('清除存储数据失败:', error.message);
      return false;
    }
  }

  /**
   * 安全的存储数据到本地 - 异步版本
   */
  async safeSetStorageAsync(key, data) {
    return new Promise((resolve) => {
      try {
        if (typeof wx !== 'undefined' && wx.setStorage) {
          wx.setStorage({
            key: key,
            data: data,
            success: () => resolve(true),
            fail: (error) => {
              console.warn('异步存储数据失败:', error.errMsg);
              resolve(false);
            }
          });
        } else {
          resolve(this.safeSetStorage(key, data));
        }
      } catch (error) {
        console.warn('异步存储数据异常:', error.message);
        resolve(false);
      }
    });
  }

  /**
   * 安全的从本地获取数据 - 异步版本
   */
  async safeGetStorageAsync(key, defaultValue = null) {
    return new Promise((resolve) => {
      try {
        if (typeof wx !== 'undefined' && wx.getStorage) {
          wx.getStorage({
            key: key,
            success: (res) => resolve(res.data || defaultValue),
            fail: (error) => {
              console.warn('异步获取存储数据失败:', error.errMsg);
              resolve(defaultValue);
            }
          });
        } else {
          resolve(this.safeGetStorage(key, defaultValue));
        }
      } catch (error) {
        console.warn('异步获取存储数据异常:', error.message);
        resolve(defaultValue);
      }
    });
  }

  /**
   * 检查文件系统健康状态
   */
  getHealthStatus() {
    return {
      available: this.isAvailable,
      manager: this.fileSystemManager !== null,
      userDataPath: this.getUserDocumentsPath() !== null,
      tempPath: this.getTempPath() !== null
    };
  }

  /**
   * 安全执行文件操作
   */
  async safeExecute(operation, fallback = null) {
    try {
      return await operation();
    } catch (error) {
      console.warn('文件操作失败:', error.message);
      return fallback;
    }
  }
}

// 创建全局实例
const safeFileSystem = new SafeFileSystem();

export default SafeFileSystem;
export { safeFileSystem };
