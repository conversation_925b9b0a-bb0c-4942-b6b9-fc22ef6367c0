/* 轨迹分析页面样式 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 头部 */
.header {
  text-align: center;
  padding: 40rpx 0;
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
}

/* 统计卡片 */
.stats-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.stats-row {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
}

/* 视图选择器 */
.view-selector {
  display: flex;
  background: white;
  border-radius: 50rpx;
  margin-bottom: 30rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.view-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 36rpx;
  font-size: 28rpx;
  border: none;
  background: transparent;
  color: #666;
  transition: all 0.3s ease;
}

.view-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

/* 轨迹容器 */
.trajectory-container {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  position: relative;
}

.trajectory-canvas {
  width: 100%;
  height: 400rpx;
  display: block;
}

.canvas-controls {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  gap: 12rpx;
}

.control-btn {
  padding: 12rpx 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #333;
  border: none;
  backdrop-filter: blur(10rpx);
}

/* 轨迹列表 */
.trajectory-list {
  margin-bottom: 30rpx;
}

.empty-state {
  background: white;
  border-radius: 20rpx;
  padding: 80rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.trajectory-item {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.trajectory-item.selected {
  background: linear-gradient(135deg, #E3F2FD 0%, #F3E5F5 100%);
  border: 2rpx solid #667eea;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.item-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.item-actions {
  display: flex;
  gap: 12rpx;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f9fa;
  border: none;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-info {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.info-text {
  font-size: 24rpx;
  color: #666;
  min-width: 160rpx;
}

/* 底部操作 */
.bottom-actions {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
  padding-bottom: 40rpx;
}

.action-button {
  flex: 1;
  min-width: 200rpx;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-button.primary {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
}

.action-button.secondary {
  background: white;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.action-button:active {
  transform: translateY(2rpx);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  min-width: 300rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 40rpx;
  display: block;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 16rpx;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #667eea;
  animation: pulse 1.5s ease-in-out infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.3s;
}

.dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* 提示信息 */
.toast {
  position: fixed;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1001;
}

.toast.show {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .stats-row {
    flex-wrap: wrap;
  }
  
  .stat-item {
    width: 50%;
    margin-bottom: 24rpx;
  }
  
  .bottom-actions {
    flex-direction: column;
  }
  
  .action-button {
    width: 100%;
  }
}