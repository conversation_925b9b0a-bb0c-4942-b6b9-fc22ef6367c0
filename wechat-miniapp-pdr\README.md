# 室内惯导定位微信小程序

基于MLA-MFL算法的微信小程序室内定位系统，移植自Android项目《MLA-MFL: A Smartphone Indoor Localization Method for Fusing Multi-source Sensors under Multiple Scene Conditions》。

## 项目概述

本项目实现了一个完整的室内惯导定位系统，集成了以下核心功能：

### 🔬 核心算法
- **PDR (Pedestrian Dead Reckoning)** - 行人航位推算
- **步态检测** - 基于加速计的步态识别与步长估计
- **航向估计** - 卡尔曼滤波(KF)和扩展卡尔曼滤波(EKF)
- **MLA节点匹配** - 多传感器地标关联算法
- **传感器融合** - 自适应权重的多传感器数据融合

### 📱 功能特性
- **实时定位** - 50Hz高频传感器数据采集与处理
- **轨迹可视化** - 2D/3D轨迹显示与交互
- **传感器校准** - 加速计、陀螺仪、罗盘一键校准
- **性能分析** - 详细的定位精度与性能统计
- **数据导出** - 轨迹数据与统计信息导出

## 技术架构

```
wechat-miniapp-pdr/
├── app.js                 # 应用入口
├── app.json              # 应用配置
├── pages/                # 页面文件
│   ├── index/           # 主页面 - 实时定位
│   ├── calibration/     # 校准页面
│   ├── tracking/        # 轨迹页面
│   └── data/           # 数据分析页面
├── utils/              # 工具类
│   ├── SensorManager.js    # 传感器数据管理
│   ├── StepDetector.js     # 步态检测器
│   ├── HeadingEstimator.js # 航向角估计器
│   ├── MLAMatcher.js       # MLA节点匹配器
│   └── PDREngine.js        # PDR定位引擎
└── project.config.json # 项目配置
```

### 核心模块说明

#### 1. 传感器数据管理 (SensorManager)
- **数据采集**: 加速计、陀螺仪、罗盘数据采集
- **数据预处理**: 滑动窗口、噪声过滤、偏置校正
- **异常检测**: 传感器故障检测与处理

#### 2. 步态检测 (StepDetector)
- **峰值检测**: 基于加速度峰值的步态识别
- **步长估计**: Weinberg模型 `SL = K × (a_max - a_min)^0.25`
- **自适应参数**: 动态调整检测阈值与步长系数

#### 3. 航向角估计 (HeadingEstimator)
- **卡尔曼滤波**: 融合陀螺仪与罗盘数据
- **自适应噪声**: 基于传感器方差的自适应调整
- **异常处理**: 新息门限检测与异常值过滤

#### 4. MLA节点匹配 (MLAMatcher)
- **多传感器节点**: 陀螺仪、气压计、加速计节点
- **距离匹配**: 欧氏距离计算与最近邻匹配
- **置信度评估**: 基于传感器特征的匹配置信度
- **位置校正**: 加权融合的位置修正

#### 5. PDR定位引擎 (PDREngine)
- **算法融合**: 集成所有子模块的主控制器
- **自适应权重**: 基于匹配质量的动态权重调整
- **性能监控**: 实时性能统计与异常检测

## 算法原理

### PDR递推定位
```javascript
// 位置更新公式
newPosition.x = currentPosition.x + stepLength * cos(heading)
newPosition.y = currentPosition.y + stepLength * sin(heading)
```

### 卡尔曼滤波航向估计
```javascript
// 状态方程: [heading, gyroBias]
// 预测: x_k = F * x_{k-1} + B * u_k
// 更新: x_k = x_k + K * (z_k - H * x_k)
```

### MLA位置融合
```javascript
// 加权融合
fusedPosition = pdrPosition * pdrWeight + mlaPosition * mlaWeight
// 自适应权重调整
if (correctionDistance > threshold) {
    mlaWeight += 0.1  // 增加MLA权重
}
```

## 使用说明

### 1. 开发环境
- 微信开发者工具 1.06.0+
- Node.js 16.0+
- 支持ES6+语法

### 2. 安装配置
1. 克隆项目到本地
2. 用微信开发者工具打开项目
3. 配置AppID（可使用测试号）
4. 真机预览测试

### 3. 使用流程
1. **传感器校准** - 首次使用需校准传感器
2. **开始定位** - 点击开始按钮启动实时定位
3. **轨迹查看** - 实时查看运动轨迹
4. **数据分析** - 查看定位精度与性能统计

### 4. 注意事项
- 需要真机测试，模拟器不支持传感器
- 首次使用建议进行传感器校准
- 定位精度受环境磁场干扰影响
- 建议在室内相对稳定的磁场环境使用

## 性能指标

### 定位精度
- **平均误差**: < 2米 (良好环境)
- **最大误差**: < 5米 (复杂环境)
- **航向误差**: < 10度

### 实时性能
- **数据采样率**: 50Hz
- **处理延迟**: < 50ms
- **内存占用**: < 10MB
- **电池消耗**: 中等 (连续使用约2-3小时)

## API文档

### PDREngine 主要接口

```javascript
// 启动定位引擎
await pdrEngine.start({
    sampleRate: 50,           // 采样频率
    initialPosition: {x: 0, y: 0, z: 0},  // 初始位置
    fusionParams: {           // 融合参数
        pdrWeight: 0.7,
        mlaWeight: 0.3
    }
});

// 设置回调函数
pdrEngine.setCallbacks({
    onPositionUpdate: (position) => { /* 位置更新 */ },
    onStepDetected: (step) => { /* 步态检测 */ },
    onMlaMatched: (match) => { /* MLA匹配 */ }
});

// 获取当前状态
const state = pdrEngine.getCurrentState();
// {position, heading, velocity, stepCount, confidence}

// 获取性能统计
const stats = pdrEngine.getStatistics();
// {totalSteps, totalDistance, matchRate, correctionRate}
```

## 数据格式

### 轨迹数据格式
```json
{
  "timestamp": 1640995200000,
  "position": {
    "x": 1.23,
    "y": 4.56,
    "z": 0.00
  },
  "heading": 45.0,
  "stepCount": 123,
  "confidence": 0.85,
  "corrected": false
}
```

### MLA节点格式
```json
{
  "id": 1,
  "x": -23.43,
  "y": 13.5,
  "z": 0,
  "floor": 4,
  "sensors": "gyro",
  "description": "4楼走廊转弯点"
}
```

## 常见问题

### Q: 定位精度不高怎么办？
A: 
1. 首先进行传感器校准
2. 确保MLA节点数据准确
3. 在磁场稳定的环境使用
4. 适当调整融合权重参数

### Q: 步态检测不准确？
A:
1. 检查加速计校准状态
2. 调整步态检测阈值
3. 确保设备正确佩戴方式
4. 避免剧烈抖动

### Q: 航向角偏差大？
A:
1. 进行罗盘8字校准
2. 远离磁场干扰源
3. 调整卡尔曼滤波参数
4. 增加陀螺仪权重

## 开发团队

本项目基于武汉大学移动地理空间大数据云服务创新团队的研究成果开发。

原始Android项目参考论文：
*MLA-MFL: A Smartphone Indoor Localization Method for Fusing Multi-source Sensors under Multiple Scene Conditions*

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

### 开发规范
- 使用ES6+语法
- 遵循微信小程序开发规范
- 添加详细的代码注释
- 提供单元测试

## 更新日志

### v1.0.0 (2024-01-01)
- 🎉 首个版本发布
- ✅ 完整的PDR算法实现
- ✅ MLA节点匹配功能
- ✅ 可视化界面
- ✅ 传感器校准功能
- ✅ 性能分析工具

---

📱 **扫码体验小程序** (开发中)

🏠 **项目主页**: [GitHub Repository]

📧 **技术支持**: [Issues Page]