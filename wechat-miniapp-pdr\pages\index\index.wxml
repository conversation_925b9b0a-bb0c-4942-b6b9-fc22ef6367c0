<!--室内惯导定位首页-->
<view class="container">
  <!-- 头部状态栏 -->
  <view class="header">
    <view class="status-bar">
      <view class="status-item">
        <text class="status-label">状态</text>
        <text class="status-value {{trackingStatus ? 'active' : ''}}">
          {{trackingStatus ? '定位中' : '已停止'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">楼层</text>
        <text class="status-value">{{currentFloor}}F</text>
      </view>
      <view class="status-item">
        <text class="status-label">步数</text>
        <text class="status-value">{{stepCount}}</text>
      </view>
    </view>
  </view>

  <!-- 轨迹显示画布 - 升级到Canvas 2D -->
  <view class="canvas-container">
    <canvas
      class="tracking-canvas"
      type="2d"
      id="trackingCanvas"
      bindtouchstart="onCanvasTouchStart"
      bindtouchmove="onCanvasTouchMove"
      bindtouchend="onCanvasTouchEnd">
    </canvas>
    
    <!-- 画布控制按钮 -->
    <view class="canvas-controls">
      <view class="control-btn" bindtap="resetView">
        <text class="iconfont icon-reset">⟲</text>
      </view>
      <view class="control-btn" bindtap="centerView">
        <text class="iconfont icon-center">⊙</text>
      </view>
      <view class="control-btn" bindtap="toggleGrid">
        <text class="iconfont icon-grid">⚏</text>
      </view>
    </view>
  </view>

  <!-- 详细信息面板 -->
  <view class="info-panel {{showDetails ? 'expanded' : ''}}" bindtap="toggleDetails">
    <view class="panel-header">
      <text class="panel-title">定位信息</text>
      <text class="expand-icon {{showDetails ? 'rotated' : ''}}">▼</text>
    </view>
    
    <view class="panel-content" wx:if="{{showDetails}}">
      <!-- 位置信息 -->
      <view class="info-section">
        <text class="section-title">当前位置</text>
        <view class="info-row">
          <text class="info-label">X坐标:</text>
          <text class="info-value">{{position.x}} m</text>
        </view>
        <view class="info-row">
          <text class="info-label">Y坐标:</text>
          <text class="info-value">{{position.y}} m</text>
        </view>
        <view class="info-row">
          <text class="info-label">Z坐标:</text>
          <text class="info-value">{{position.z}} m</text>
        </view>
      </view>

      <!-- 运动信息 -->
      <view class="info-section">
        <text class="section-title">运动状态</text>
        <view class="info-row">
          <text class="info-label">航向角:</text>
          <text class="info-value">{{heading}}°</text>
        </view>
        <view class="info-row">
          <text class="info-label">步长:</text>
          <text class="info-value">{{stepLength}} m</text>
        </view>
        <view class="info-row">
          <text class="info-label">速度:</text>
          <text class="info-value">{{velocity}} m/s</text>
        </view>
      </view>

      <!-- 传感器信息 -->
      <view class="info-section">
        <text class="section-title">传感器状态</text>
        <view class="info-row">
          <text class="info-label">加速度:</text>
          <text class="info-value">{{accelerometer.magnitude}} m/s²</text>
        </view>
        <view class="info-row">
          <text class="info-label">陀螺仪:</text>
          <text class="info-value">{{gyroscope.magnitude}} rad/s</text>
        </view>
        <view class="info-row">
          <text class="info-label">罗盘:</text>
          <text class="info-value">{{compass.direction}}°</text>
        </view>
      </view>

      <!-- MLA匹配信息 -->
      <view class="info-section" wx:if="{{lastMatch}}">
        <text class="section-title">最近匹配</text>
        <view class="info-row">
          <text class="info-label">节点类型:</text>
          <text class="info-value">{{lastMatch.type}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">置信度:</text>
          <text class="info-value">{{lastMatch.confidence}}%</text>
        </view>
        <view class="info-row">
          <text class="info-label">描述:</text>
          <text class="info-value">{{lastMatch.node.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 控制按钮区域 -->
  <view class="controls">
    <button 
      class="control-button {{trackingStatus ? 'stop' : 'start'}}"
      bindtap="toggleTracking">
      {{trackingStatus ? '停止定位' : '开始定位'}}
    </button>
    
    <view class="button-group">
      <button class="secondary-button" bindtap="calibrateSensors">
        校准传感器
      </button>
      <button class="secondary-button" bindtap="resetTracking">
        重置轨迹
      </button>
    </view>
  </view>

  <!-- 加载指示器 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>

  <!-- 提示信息 -->
  <view class="toast {{showToast ? 'show' : ''}}" bindtap="hideToast">
    <text>{{toastMessage}}</text>
  </view>
</view>